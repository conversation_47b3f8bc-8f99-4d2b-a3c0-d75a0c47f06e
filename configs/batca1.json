{"account": "************", "region": "ca-central-1", "environment": "batca1", "tags": {"tower": "operations", "department-id": "1904", "department-name": "ops-digital", "CostCode": "1904", "ProjectName": "ODH", "SharedResource": "Yes", "Application": "PNR Store - ODH Shared Resources", "TechOwner": "<PERSON><PERSON><PERSON>", "BusinessOwner": "<PERSON><PERSON><PERSON>", "Criticality": "Critical", "Sensitivity": "High", "RecoveryTimeObjective": "< 15 mins", "RecoveryPointObjective": "< 15 mins", "Type": "Customer", "BusinessImpact": "High", "ComplianceRequirement": "NA", "Observability": "Yes", "Environment": "BATCH"}, "vpc": {"vpcId": "vpc-0abed1f5dc9bec60b", "securityGroupId": "sg-0993d7143c57a6f80"}, "iamRole": {"ingestionLambdaRoleExportName": "ac-odh-common-resources-roles-batca1-ingestionLambdaRoleArn"}, "secretsManager": {"traxDbSecret": "batca1/trax-external-db/db-credentials", "dynatraceCredentialsSecretName": "/bat/dbaas/dynatrace/oneagent_credentials", "traxDbProxySecretName": "/batca1/trax-db-proxy/api-credential"}, "lambda": {"timeout": 300, "memorySize": 1024, "sreLoggerLayerExportName": "sls-dbaas-sre-infra-v3-layer-batca1-SRELoggingAPIExtensionLayerLambdaLayerQualifiedArn", "logging": {"retentionDays": 14, "layerEnabled": "true", "lambdaLogMode": "debug", "debugMode": "false", "logStreamName": "SLSInfo", "extensionLogDestinationBucketName": " dbaas-sre-v3-logs-batca1-default", "layerDebugMode": "false"}}, "s3": {"distributionBucketName": "ac-odh-batch-distribution-batca1", "webfocusBucketName": "ac-maintenance-ops-webfocus-uat-files"}, "filePaths": {"vendorGeneratingPath": "WEB_FOCUS/VENDOR/", "vendorUploadingPath": "WEB_FOCUS/VENDOR/", "woGeneratingPath": "WEB_FOCUS/WO/", "woUploadingPath": "WEB_FOCUS/WO/", "poGeneratingPath": "WEB_FOCUS/PO/", "poUploadingPath": "WEB_FOCUS/PO/", "taskCardGeneratingPath": "WEB_FOCUS/TCC/", "taskCardUploadingPath": "WEB_FOCUS/TCC/", "engineDataGeneratingPath": "WEB_FOCUS/Engines/", "engineDataUploadingPath": "WEB_FOCUS/Engines/", "smcWoDataGeneratingPath": "WEB_FOCUS/SMC_WO/", "smcWoDataUploadingPath": "WEB_FOCUS/SMC_WO/", "pendingPath": "PENDING/", "processedPath": "PROCESSED/", "filePrefixVendor": "TRAX_VENDOR_BAT", "filePrefixWO": "TRAX_WO_BAT", "filePrefixPO": "TRAX_PO_BAT", "filePrefixTaskCard": "TRAX_TCC_BAT", "filePrefixEngines": "TRAX_Engines_BAT", "filePrefixDeletedWoData": "TRAX_DELETED_WO_BAT", "filePrefixSmcWoData": "TRAX_SMC_WO_BAT"}, "defaultStackSynthesizer": {"cdkBootstrapConfigSSM": "/dbaas/bat-cac1/cdk-bootstrap/config", "bucketPrefix": "ac-odh/ac-odh-batch-distribution-webfocus/batca1/"}, "stacks": {"CommonResourceStack": {"stackName": "ac-odh-batch-distribution-webfocus-common-resources", "resources": {"s3UploaderLambda": {"functionName": "ac-odh-webfocus-common-resources-uploader"}, "errorNotifyTopic": {"topicName": "ac-odh-webfocus-common-resources-error-notify-topic"}, "eventBridgeRule": {"ruleName": "ac-odh-webfocus-common-resources-s3-uploader-rule"}, "s3UploaderLambdaRole": {"roleName": "ac-odh-webfocus-common-resources"}}, "notifications": {"errorNotifyEmail": "<EMAIL>"}}, "DeltaLoadExtractionStack": {"stackName": "ac-odh-batch-distribution-webfocus-delta-load-extraction", "resources": {"vendorExtractionLambda": {"functionName": "ac-odh-webfocus-delta-load-extraction-vendor"}, "woExtractionLambda": {"functionName": "ac-odh-webfocus-delta-load-extraction-wo"}, "poExtractionLambda": {"functionName": "ac-odh-webfocus-delta-load-extraction-po"}, "taskCardExtractionLambda": {"functionName": "ac-odh-webfocus-delta-load-extraction-task-card"}, "engineDataExtractionLambda": {"functionName": "ac-odh-webfocus-delta-load-extraction-engine-data"}, "smcWoDataExtractionLambda": {"functionName": "ac-odh-webfocus-delta-load-extraction-smc-wo-data"}, "lambdaRole": {"roleName": "ac-odh-webfocus-delta-load-extraction-lambda-role"}, "stateMachine": {"stateMachineName": "ac-odh-webfocus-delta-load-extraction-workflow", "roleName": "ac-odh-webfocus-delta-load-extraction-state-machine-role", "schedule": {"expression": "rate(3 hours)", "enabled": true}}, "eventRule": {"ruleName": "ac-odh-webfocus-delta-load-extraction-schedule-rule"}}, "database": {"connectionRetryAttempts": 3, "minPoolSize": 1, "maxPoolSize": 3, "knexDebug": false}}, "InitialLoadExtractionStack": {"stackName": "ac-odh-batch-distribution-webfocus-initial-load-extraction", "params": {"GLUE_JOB_CONFIG_MAXCONCURRENTRUNS": 3, "GLUE_JOB_CONFIG_WORKERTYPE": "G.1X", "GLUE_JOB_CONFIG_NUMBEROFWORKERS": 2, "GLUE_JOB_CONFIG_TIMEOUT": 2880, "GLUE_JOB_CONFIG_MAXRETRIES": 2, "ENABLE_GLUE_CLOUDWATCH_LOGS": "true", "GLUE_SPARK_UI_LOGS_PATH": "s3://ac-odh-glue-jobs-scripts-common-batca1/spark-ui/", "GLUE_UTILS_PATH": "s3://ac-odh-common-glue-utils-batca1/aws-glue-utils/v1.1.0/glue_utils.zip", "START_DATE": "20170101", "END_DATE": "20250804", "GLUE_CONNECTION_NAME": "ods_maintenanceOperationsDataStore_reader_proxy"}}}}