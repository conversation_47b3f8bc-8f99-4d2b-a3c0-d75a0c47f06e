import { DefaultStackSynthesizer } from "aws-cdk-lib";

export interface CommonResourceStackConfig {
  stackName: string;
  resources: {
    s3UploaderLambda: {
      functionName: string;
    };
    errorNotifyTopic: {
      topicName: string;
    };
    eventBridgeRule: {
      ruleName: string;
    };
    s3UploaderLambdaRole: {
      roleName: string;
    };
  };
  notifications: {
    errorNotifyEmail: string;
  };
}

export interface DeltaLoadExtractionStackConfig {
  stackName: string;
  resources: {
    vendorExtractionLambda: {
      functionName: string;
    };
    woExtractionLambda: {
      functionName: string;
    };
    poExtractionLambda: {
      functionName: string;
    };
    taskCardExtractionLambda: {
      functionName: string;
    };
    engineDataExtractionLambda: {
      functionName: string;
    };
    smcWoDataExtractionLambda: {
      functionName: string;
    };
    lambdaRole: {
      roleName: string;
    };
    stateMachine: {
      stateMachineName: string;
      roleName: string;
      schedule: {
        expression: string;
        enabled: boolean;
      };
    };
    eventRule: {
      ruleName: string;
    };
  };
  database: {
    connectionRetryAttempts: number;
    minPoolSize: number;
    maxPoolSize: number;
    knexDebug: boolean;
  };
}

export interface InitialLoadExtractionStackConfig {
  stackName: string;
  params: {
    GLUE_JOB_CONFIG_MAXCONCURRENTRUNS: number;
    GLUE_JOB_CONFIG_WORKERTYPE: string;
    GLUE_JOB_CONFIG_NUMBEROFWORKERS: number;
    GLUE_JOB_CONFIG_TIMEOUT: number;
    GLUE_JOB_CONFIG_MAXRETRIES: number;
    ENABLE_GLUE_CLOUDWATCH_LOGS: string;
    GLUE_SPARK_UI_LOGS_PATH: string;
    GLUE_UTILS_PATH: string;
    START_DATE: string;
    END_DATE: string;
    GLUE_CONNECTION_NAME: string;
  };
}

export interface AppConfig {
  account: string;
  region: string;
  environment: string;
  tags: {
    tower: string;
    "department-id": string;
    "department-name": string;
    CostCode: string;
    ProjectName: string;
    SharedResource: string;
    Application: string;
    TechOwner: string;
    BusinessOwner: string;
    Criticality: string;
    Sensitivity: string;
    RecoveryTimeObjective: string;
    RecoveryPointObjective: string;
    Type: string;
    BusinessImpact: string;
    ComplianceRequirement: string;
    Observability: string;
    Environment: string;
  };
  vpc: {
    vpcId: string;
    securityGroupId: string;
  };
  iamRole: {
    ingestionLambdaRoleExportName: string;
  };
  secretsManager: {
    traxDbSecret: string;
    dynatraceCredentialsSecretName: string;
    traxDbProxySecretName: string;
  };
  lambda: {
    timeout: number;
    memorySize: number;
    sreLoggerLayerExportName: string;
    logging: {
      retentionDays: number;
      layerEnabled: string;
      lambdaLogMode: string;
      debugMode: string;
      logStreamName: string;
      extensionLogDestinationBucketName: string;
      layerDebugMode: string;
    };
  };
  s3: {
    distributionBucketName: string;
    webfocusBucketName: string;
  };
  filePaths: {
    vendorGeneratingPath: string;
    vendorUploadingPath: string;
    woGeneratingPath: string;
    woUploadingPath: string;
    poGeneratingPath: string;
    poUploadingPath: string;
    taskCardGeneratingPath: string;
    taskCardUploadingPath: string;
    engineDataGeneratingPath: string;
    engineDataUploadingPath: string;
    smcWoDataGeneratingPath: string;
    smcWoDataUploadingPath: string;
    pendingPath: string;
    processedPath: string;
    filePrefixVendor: string;
    filePrefixWO: string;
    filePrefixPO: string;
    filePrefixTaskCard: string;
    filePrefixEngines: string;
    filePrefixDeletedWoData: string;
    filePrefixSmcWoData: string;
  };
  defaultStackSynthesizer: {
    cdkBootstrapConfigSSM: string;
    bucketPrefix: string;
  };
  stacks: {
    CommonResourceStack: CommonResourceStackConfig;
    DeltaLoadExtractionStack: DeltaLoadExtractionStackConfig;
    InitialLoadExtractionStack: InitialLoadExtractionStackConfig;
  };
}

export { DefaultStackSynthesizer };
