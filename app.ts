import "source-map-support/register";
import * as cdk from "aws-cdk-lib";

import {
  createSynthesizer,
  SynthesizerSSMConfig,
} from "./synthesizer/create-synthesizer";
import { applyTagsToStacks, sanitizeTags } from "./utils/cdk-tagging";
import { getConfig, AppConfig } from "./utils/config-util";

import { CommonResourceStack } from "./stacks/common-resources/common-resources-stack";
import { DeltaLoadExtractionStack } from "./stacks/delta-load-extraction/delta-load-extraction-stack";
import { InitialLoadExtractionStack } from "./stacks/initial-load-extraction/initial-load-extraction-stack";

async function main() {
  const app = new cdk.App();
  const environment = app.node.tryGetContext("stage") || "intca1";
  const config: AppConfig = getConfig(environment);

  const synthesizerConfig: SynthesizerSSMConfig =
    config.defaultStackSynthesizer;
  const synthesizer = await createSynthesizer(synthesizerConfig);

  const commonResorceStackConfigProps = config.stacks.CommonResourceStack;

  const commonResourceStack = new CommonResourceStack(
    app,
    `${commonResorceStackConfigProps.stackName}-${environment}`,
    {
      config,
      synthesizer,
      stackConfigProps: commonResorceStackConfigProps,
      env: {
        account: config.account,
        region: config.region,
      },
      tags: config.tags,
    }
  );

  const initialLoadExtractionStack = new InitialLoadExtractionStack(
    app,
    `${config.stacks.InitialLoadExtractionStack.stackName}-${environment}`,
    {
      config,
      synthesizer,
      stackConfigProps: config.stacks.InitialLoadExtractionStack,
      errorNotifyTopicArn: commonResourceStack.errorNotifyTopic.topicArn,
      env: {
        account: config.account,
        region: config.region,
      },
      tags: config.tags,
    }
  );

  const deltaLoadExtractionStack = new DeltaLoadExtractionStack(
    app,
    `${config.stacks.DeltaLoadExtractionStack.stackName}-${environment}`,
    {
      config,
      synthesizer,
      stackConfigProps: config.stacks.DeltaLoadExtractionStack,
      errorNotifyTopicArn: commonResourceStack.errorNotifyTopic.topicArn,
      env: {
        account: config.account,
        region: config.region,
      },
      tags: config.tags,
    }
  );

  const sanitizedTags = sanitizeTags(config.tags);
  applyTagsToStacks(
    [commonResourceStack, initialLoadExtractionStack, deltaLoadExtractionStack],
    sanitizedTags
  );

  app.synth();
}

main().catch((err) => {
  console.error("CDK App failed:", err);
  process.exit(1);
});
