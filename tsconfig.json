{"compilerOptions": {"target": "ES2018", "module": "CommonJS", "moduleResolution": "node", "lib": ["es2018", "dom"], "declaration": true, "strict": true, "noImplicitAny": false, "strictNullChecks": false, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "inlineSourceMap": true, "inlineSources": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "typeRoots": ["./node_modules/@types"], "outDir": "dist", "rootDir": ".", "esModuleInterop": true, "resolveJsonModule": true}, "exclude": ["node_modules", "cdk.out", "dist"], "include": ["bin/**/*", "lib/**/*", "stacks/**/*", "app.ts"]}