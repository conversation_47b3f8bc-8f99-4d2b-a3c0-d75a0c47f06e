import * as lambda from "aws-cdk-lib/aws-lambda";
import * as lambdaNodejs from "aws-cdk-lib/aws-lambda-nodejs";
import * as secretsmanager from "aws-cdk-lib/aws-secretsmanager";
import * as logs from "aws-cdk-lib/aws-logs";

import { Duration, RemovalPolicy, StackProps, Fn } from "aws-cdk-lib";
import { Construct } from "constructs";
import { ISecurityGroup, IVpc } from "aws-cdk-lib/aws-ec2";
import { IRole } from "aws-cdk-lib/aws-iam";
import { getConfig, AppConfig } from "../utils/config-util";

export interface SharedLambdaConstructProps extends StackProps {
  environment: string;
  vpc: IVpc;
  securityGroup: ISecurityGroup;
  functionName: string;
  logging: {
    retentionDays?: number;
  };
  memorySize?: number;
  timeout?: number;
  handlerFilePath: string;
  handlerFunctionName: string;
  description: string;
  envVariables?: Record<string, string>;
  region: string;
  role?: IRole;
  externalModules?: string[];
}

export class SharedLambdaConstruct extends Construct {
  public readonly function: lambdaNodejs.NodejsFunction;

  constructor(scope: Construct, id: string, props: SharedLambdaConstructProps) {
    super(scope, id);

    const functionName = props.functionName;

    const config: AppConfig = getConfig(props.environment);

    const paramsAndSecrets = lambda.ParamsAndSecretsLayerVersion.fromVersion(
      lambda.ParamsAndSecretsVersions.V1_0_103,
      {
        cacheSize: 500,
        logLevel: lambda.ParamsAndSecretsLogLevel.ERROR,
        secretsManagerTtl: Duration.seconds(300),
      }
    );

    const logGroup = new logs.LogGroup(this, `${functionName}-log-group`, {
      logGroupName: `/aws/lambda/${functionName}`,
      retention: props.logging.retentionDays || 7,
      removalPolicy: RemovalPolicy.DESTROY,
      logGroupClass: logs.LogGroupClass.STANDARD,
    });

    const dynatraceSecret = secretsmanager.Secret.fromSecretNameV2(
      this,
      "DynatraceSecret",
      config.secretsManager.dynatraceCredentialsSecretName
    );

    const sreLoggingLayerArn = Fn.importValue(
      config.lambda.sreLoggerLayerExportName
    );

    this.function = new lambdaNodejs.NodejsFunction(scope, functionName, {
      runtime: lambda.Runtime.NODEJS_20_X,
      entry: props.handlerFilePath,
      handler: props.handlerFunctionName,
      description: props.description,
      functionName: `${functionName}`,
      memorySize: props.memorySize || 1024,
      timeout: Duration.seconds(props.timeout || 900),
      vpc: props.vpc,
      securityGroups: [props.securityGroup],
      logGroup,
      bundling: {
        externalModules:
          props.externalModules && props.externalModules.length > 0
            ? props.externalModules
            : [],
      },
      paramsAndSecrets,
      role: props.role,
      environment: {
        AWS_LAMBDA_EXEC_WRAPPER: dynatraceSecret
          .secretValueFromJson("AWS_LAMBDA_EXEC_WRAPPER")
          .unsafeUnwrap(),
        DT_TENANT: dynatraceSecret
          .secretValueFromJson("DT_TENANT")
          .unsafeUnwrap(),
        DT_CLUSTER_ID: dynatraceSecret
          .secretValueFromJson("DT_CLUSTER_ID")
          .unsafeUnwrap(),
        DT_CONNECTION_BASE_URL: dynatraceSecret
          .secretValueFromJson("DT_CONNECTION_BASE_URL")
          .unsafeUnwrap(),
        DT_CONNECTION_AUTH_TOKEN: dynatraceSecret
          .secretValueFromJson("DT_CONNECTION_AUTH_TOKEN")
          .unsafeUnwrap(),
        LAYER_ENABLED: config.lambda.logging.layerEnabled,
        LAMBDA_LOG_MODE: config.lambda.logging.lambdaLogMode,
        DEBUG_MODE: config.lambda.logging.debugMode,
        LOG_STREAM_NAME: config.lambda.logging.logStreamName,
        EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME:
          config.lambda.logging.extensionLogDestinationBucketName,
        LAYER_DEBUG_MODE: config.lambda.logging.layerDebugMode,
        ...props.envVariables,
      },
      layers: [
        lambda.LayerVersion.fromLayerVersionArn(
          this,
          "sreLoggingLayer",
          sreLoggingLayerArn
        ),
        lambda.LayerVersion.fromLayerVersionArn(
          this,
          "DynatraceOneAgentLayer",
          `arn:aws:lambda:${props.region}:725887861453:layer:Dynatrace_OneAgent_1_295_3_20240729-145043_nodejs:1`
        ),
      ],
    });
  }
}
