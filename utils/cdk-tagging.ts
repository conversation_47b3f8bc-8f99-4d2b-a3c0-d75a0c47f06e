import { Tags, Stack } from "aws-cdk-lib";
import { Construct } from "constructs";

function applyTags(resource: Stack, tags: Record<string, string>): void {
  for (const [key, value] of Object.entries(tags)) {
    Tags.of(resource).add(key, value);
  }
}

function sanitizeTagValue(value: string): string {
  return value.replace(/[^a-zA-Z0-9\s._:/=+\-@]/g, "").trim();
}

/**
 * Applies tags to multiple stacks at once.
 * @param stacks - Array of CDK stacks or constructs
 * @param tags - Record of tag key/value pairs
 */
function applyTagsToStacks(stacks: Stack[], tags: Record<string, string>): void {
  stacks.forEach((stack) => applyTags(stack, tags));
}

/**
 * Sanitizes all tag values in the given tag object.
 * Example:
 *   Input:  { Project: "My@App!", Owner: " <EMAIL> " }
 *   Output: { Project: "MyApp", Owner: "<EMAIL>" }
 */
function sanitizeTags(tags: Record<string, string>): Record<string, string> {
  const sanitized: Record<string, string> = {};
  for (const [key, value] of Object.entries(tags)) {
    sanitized[key] = sanitizeTagValue(value);
  }
  return sanitized;
}

function addResourceLevelTags (
  construct: Construct,
  awsService: string,
  uniqueId: string
) {
  Tags.of(construct).add("aws-service", awsService);
  Tags.of(construct).add("unique-id", uniqueId);
};

export {
  applyTagsToStacks,
  sanitizeTags,
  addResourceLevelTags
}