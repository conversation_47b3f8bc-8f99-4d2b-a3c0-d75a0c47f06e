name: CDK Deployment
on:
  push:
    branches: 
      - main
      - release
      - bat
      - develop
      - dev
      - feature/*
      - hotfix/*
  workflow_dispatch:
    inputs:
      Deployment_Environment:
        description: "Deploy to Environment"
        required: true
        default: 'intca1'
        type: choice
        options:
          - intca1
          - crtca1
          - batca1
          - preprodca1
          - prodca1

permissions:
    id-token: write
    contents: read
    packages: write
    pull-requests: write

jobs:
  INT:
    if: ((github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/dev' || startsWith(github.ref, 'refs/heads/feature/')) && github.event_name != 'workflow_dispatch' ) || github.event.inputs.Deployment_Environment == 'intca1'
    uses: "AC-IT-Development/Dbaas-Reusable-GitHub-Actions/.github/workflows/DeployCDK_ODH.yml@main"
    with:
      gh_environment: intca1
      additional_commands: ""
      deploy_path: "."
      nodejs_version: '20'
      aws_region: "ca-central-1" # us-east-2
      package_manager: 'npm'  # npm or yarn
    secrets:
      AWS_OIDC_ROLE_ARN: ${{ vars.AC_DBAAS_INT_BE_OIDC_ROLE_ARN }}
      PRIVATEKEYS: ${{ secrets.SERVICE_AC_PVT_SSH_KEY }}
  CRT:
    if: ( (github.ref == 'refs/heads/release' || startsWith(github.ref, 'refs/heads/hotfix/')) && github.event_name != 'workflow_dispatch' ) || github.event.inputs.Deployment_Environment == 'crtca1'
    uses: "AC-IT-Development/Dbaas-Reusable-GitHub-Actions/.github/workflows/DeployCDK.yml@main"
    with:
      additional_commands: ""
      gh_environment: crtca1
      deploy_path: "."
      aws_region: "ca-central-1"
      package_manager: "npm"
    secrets:
      AWS_OIDC_ROLE_ARN: ${{ vars.AC_DBAAS_CRT_BE_OIDC_ROLE_ARN }}
      PRIVATEKEYS: ${{ secrets.SERVICE_AC_PVT_SSH_KEY }}
  BAT:
    if: ( github.ref == 'refs/heads/bat'  && github.event_name != 'workflow_dispatch' ) || github.event.inputs.Deployment_Environment == 'bat'
    uses: "AC-IT-Development/Dbaas-Reusable-GitHub-Actions/.github/workflows/DeployCDK.yml@main"
    with:
      additional_commands: ""
      gh_environment: batca1
      deploy_path: "."
      aws_region: "ca-central-1"
      package_manager: "npm"
    secrets:
      AWS_OIDC_ROLE_ARN: ${{ vars.AC_DBAAS_BAT_BE_OIDC_ROLE_ARN }}
      PRIVATEKEYS: ${{ secrets.SERVICE_AC_PVT_SSH_KEY }}
  PREPROD:
    if: (( github.ref == 'refs/heads/release' || startsWith(github.ref, 'refs/heads/hotfix/') )  && github.event_name != 'workflow_dispatch' ) || github.event.inputs.Deployment_Environment == 'preprodca1'
    uses: "AC-IT-Development/Dbaas-Reusable-GitHub-Actions/.github/workflows/DeployCDK.yml@main"
    with:
      additional_commands: ""
      gh_environment: preprodca1
      deploy_path: "."
      aws_region: "ca-central-1"
      package_manager: "npm"
    secrets:
      AWS_OIDC_ROLE_ARN: ${{ vars.AC_DBAAS_PREPROD_BE_OIDC_ROLE_ARN }}
      PRIVATEKEYS: ${{ secrets.SERVICE_AC_PVT_SSH_KEY }}
      
  PROD:
    if: ( github.ref == 'refs/heads/main'  && github.event_name != 'workflow_dispatch' ) || github.event.inputs.Deployment_Environment == 'prodca1'
    uses: "AC-IT-Development/Dbaas-Reusable-GitHub-Actions/.github/workflows/DeployCDK.yml@main"
    with:
      additional_commands: ""
      gh_environment: prodca1
      deploy_path: "."
      aws_region: "ca-central-1"
      package_manager: "npm"
    secrets:
      AWS_OIDC_ROLE_ARN: ${{ vars.AC_DBAAS_PROD_BE_OIDC_ROLE_ARN }}
      PRIVATEKEYS: ${{ secrets.SERVICE_AC_PVT_SSH_KEY }}