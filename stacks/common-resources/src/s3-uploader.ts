import { Context, EventBridgeEvent } from 'aws-lambda';
import {
  S3Client,
  CopyObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
} from "@aws-sdk/client-s3";
import { SNSClient, PublishCommand } from "@aws-sdk/client-sns";
import * as fastcsv from "fast-csv";
import { LOGGER ,constant} from "./utils";

// Environment variables (matching the original serverless function)
const WEBFOCUS_S3_BUCKET = process.env.WEBFOCUS_S3_BUCKET!;
const VENDOR_GENERATING_FILE_PATH = process.env.VENDOR_GENERATING_FILE_PATH!;
const VENDOR_UPLOADING_FILE_PATH = process.env.VENDOR_UPLOADING_FILE_PATH!;
const WO_GENERATING_FILE_PATH = process.env.WO_GENERATING_FILE_PATH!;
const WO_UPLOADING_FILE_PATH = process.env.WO_UPLOADING_FILE_PATH!;
const PO_GENERATING_FILE_PATH = process.env.PO_GENERATING_FILE_PATH!;
const PO_UPLOADING_FILE_PATH = process.env.PO_UPLOADING_FILE_PATH!;
const TASK_CARD_GENERATING_FILE_PATH = process.env.TASK_CARD_GENERATING_FILE_PATH!;
const TASK_CARD_UPLOADING_FILE_PATH = process.env.TASK_CARD_UPLOADING_FILE_PATH!;
const ENGINE_DATA_GENERATING_FILE_PATH = process.env.ENGINE_DATA_GENERATING_FILE_PATH!;
const ENGINE_DATA_UPLOADING_FILE_PATH = process.env.ENGINE_DATA_UPLOADING_FILE_PATH!;
const SMC_WO_DATA_GENERATING_FILE_PATH = process.env.SMC_WO_DATA_GENERATING_FILE_PATH!;
const SMC_WO_DATA_UPLOADING_FILE_PATH = process.env.SMC_WO_DATA_UPLOADING_FILE_PATH!;
const PROCESSED_FILE_GENERATING_PATH = process.env.PROCESSED_FILE_GENERATING_PATH!;
const SNS_TOPIC_ARN = process.env.SNS_TOPIC_ARN!;

interface S3ObjectCreatedEvent {
  source: string;
  'detail-type': string;
  detail: {
    bucket: {
      name: string;
    };
    object: {
      key: string;
    };
  };
}

export const handler = async (
  event: EventBridgeEvent<'Object Created', S3ObjectCreatedEvent>,
  context: Context
): Promise<void> => {
  LOGGER.initialize(event, context, constant.SERVICE_TYPE, constant.SERVICE_NAME_UPLOADER);

  try {
    const triggeringBucket = event?.detail?.bucket?.name;
    const triggeringObjectKey = event?.detail?.object?.key;
    
    if (!triggeringBucket || !triggeringObjectKey) {
      throw new Error('Missing bucket name or object key in event');
    }

    const fileName = triggeringObjectKey.split("/").pop()!;
    const fileNamePrefix = triggeringObjectKey.split("/").slice(0, -2).join("/") + "/";

    await validateCSVFile(triggeringBucket, triggeringObjectKey);

    let uploadingFolderPath: string;
    switch (fileNamePrefix) {
      case VENDOR_GENERATING_FILE_PATH:
        uploadingFolderPath = VENDOR_UPLOADING_FILE_PATH;
        break;
      case WO_GENERATING_FILE_PATH:
        uploadingFolderPath = WO_UPLOADING_FILE_PATH;
        break;
      case PO_GENERATING_FILE_PATH:
        uploadingFolderPath = PO_UPLOADING_FILE_PATH;
        break;
      case TASK_CARD_GENERATING_FILE_PATH:
        uploadingFolderPath = TASK_CARD_UPLOADING_FILE_PATH;
        break;
      case ENGINE_DATA_GENERATING_FILE_PATH:
        uploadingFolderPath = ENGINE_DATA_UPLOADING_FILE_PATH;
        break;
      case SMC_WO_DATA_GENERATING_FILE_PATH:
        uploadingFolderPath = SMC_WO_DATA_UPLOADING_FILE_PATH;
        break;
      default:
        throw new Error(`Unknown file path prefix: ${fileNamePrefix}`);
    }

    const uploadingBucketName = WEBFOCUS_S3_BUCKET;

    LOGGER.info(
      `Copying file ${triggeringObjectKey} from ${triggeringBucket} to ${uploadingFolderPath} in ${uploadingBucketName}`
    );

    const s3Client = new S3Client({});

    const command = new CopyObjectCommand({
      Bucket: uploadingBucketName,
      CopySource: `${triggeringBucket}/${triggeringObjectKey}`,
      Key: `${uploadingFolderPath}${fileName}`,
    });

    const data = await s3Client.send(command);

    // Move to Processed folder after uploading to target bucket
    if (data.$metadata.httpStatusCode === 200) {
      const copyCommand = new CopyObjectCommand({
        Bucket: triggeringBucket,
        CopySource: `${triggeringBucket}/${triggeringObjectKey}`,
        Key: `${fileNamePrefix}${PROCESSED_FILE_GENERATING_PATH}${fileName}`,
      });
      await s3Client.send(copyCommand);

      const deleteCommand = new DeleteObjectCommand({
        Bucket: triggeringBucket,
        Key: triggeringObjectKey,
      });
      await s3Client.send(deleteCommand);
    }
    
    LOGGER.info("S3 object copied successfully");
  } catch (error) {
    LOGGER.error("Error happen while uploading into webfocus bucket", error);
    await publishMessage({
      Message: error instanceof Error ? error.message : String(error),
      Subject: "Error uploading file to webfocus bucket",
      TopicArn: SNS_TOPIC_ARN,
    });
    throw error;
  }
};

const validateCSVFile = async (triggeringBucket: string, triggeringObjectKey: string): Promise<void> => {
  try {
    const s3Client = new S3Client({});
    const getCommand = new GetObjectCommand({
      Bucket: triggeringBucket,
      Key: triggeringObjectKey,
    });
    const { Body } = await s3Client.send(getCommand);

    if (!Body) {
      throw new Error(
        `Unable to find the file in the S3 bucket: ${triggeringObjectKey}`
      );
    }

    const jsonCSV = await parseCSV(Body);
    LOGGER.info(`Data extracted from csv: ${jsonCSV.length}`);
    if (jsonCSV.length === 0) {
      throw new Error(`File is empty ${triggeringObjectKey}`);
    }
    LOGGER.info(`Successfully validated the file: ${triggeringObjectKey}`);
  } catch (error) {
    LOGGER.error("Error happen while validating csv file", error);
    throw error;
  }
};

const parseCSV = async (responseBody: any): Promise<any[]> => {
  const csvContent = await new Promise<string>((resolve, reject) => {
    let data = "";
    responseBody.on("data", (chunk: any) => (data += chunk));
    responseBody.on("end", () => resolve(data));
    responseBody.on("error", (err: any) => reject(err));
  });

  return new Promise((resolve, reject) => {
    try {
      const data: any[] = [];
      fastcsv
        .parseString(csvContent)
        .on("data", (raw: any) => {
          data.push(raw);
        })
        .on("end", (rawCount: number) => {
          LOGGER.info(`Data extracted from csv raw count: ${rawCount}`);
          resolve(data);
        })
        .on("error", (error: any) => reject(error));
    } catch (error) {
      reject(error);
    }
  });
};

const publishMessage = async (params: {
  Message: string;
  Subject: string;
  TopicArn: string;
}): Promise<any> => {
  const sns = new SNSClient({});
  const command = new PublishCommand(params);
  return sns.send(command);
};
