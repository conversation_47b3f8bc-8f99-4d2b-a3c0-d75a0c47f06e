import * as sns from "aws-cdk-lib/aws-sns";
import * as snsSubscriptions from "aws-cdk-lib/aws-sns-subscriptions";
import { StackProps } from "aws-cdk-lib";
import { Construct } from "constructs";

import {
  AppConfig,
  CommonResourceStackConfig,
} from "../../../configs/config.interface";
import { addResourceLevelTags } from "../../../utils/cdk-tagging";

export interface ErrorNotificationStackProps extends StackProps {
  config: AppConfig;
  stackConfigProps: CommonResourceStackConfig;
}

export class ErrorNotificationStack extends Construct {
  public readonly topic: sns.Topic;

  constructor(scope: Construct, id: string, props: ErrorNotificationStackProps) {
    super(scope, id);

    const envName = props.config.environment;

    // Create SNS Topic for error notifications (matching serverless.yml)
    this.topic = new sns.Topic(this, "ErrorNotifyTopic", {
      topicName: `${props.stackConfigProps.resources.errorNotifyTopic.topicName}-${envName}`,
      displayName: `${props.stackConfigProps.resources.errorNotifyTopic.topicName}-${envName}`
    });

    // this.topic.addSubscription(
    //   new snsSubscriptions.EmailSubscription(props.stackConfigProps.notifications.errorNotifyEmail)
    // );

    console.log('----------->',props.stackConfigProps)

    this.topic.addSubscription(
      new snsSubscriptions.EmailSubscription("<EMAIL>")
    );

    // Add resource-specific tags
    addResourceLevelTags(this.topic, "sns", `${props.stackConfigProps.resources.errorNotifyTopic.topicName}-${envName}`);
  }
}
