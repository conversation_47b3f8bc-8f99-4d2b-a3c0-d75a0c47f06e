import * as iam from "aws-cdk-lib/aws-iam";
import { StackProps } from "aws-cdk-lib";
import { Construct } from "constructs";
import { ISecurityGroup, IVpc } from "aws-cdk-lib/aws-ec2";
import path from "path";
import { SharedLambdaConstruct } from '../../../sharedConstruct';

import {
  AppConfig,
  CommonResourceStackConfig,
} from "../../../configs/config.interface";
import { addResourceLevelTags } from "../../../utils/cdk-tagging";

export interface S3UploaderLambdaStackProps extends StackProps {
  config: AppConfig;
  stackConfigProps: CommonResourceStackConfig;
  vpc: IVpc;
  securityGroup: ISecurityGroup;
  errorNotifyTopicArn: string;
}

export class S3UploaderLambdaStack extends Construct {
  public readonly function: any;
  public readonly role: iam.Role;

  constructor(scope: Construct, id: string, props: S3UploaderLambdaStackProps) {
    super(scope, id);

    const envName = props.config.environment;
    const functionName = props.stackConfigProps.resources.s3UploaderLambda.functionName + '-' + envName;

    // Create IAM Role with all required permissions from serverless.yml
    this.role = new iam.Role(this, "S3UploaderLambdaRole", {
      roleName: `${props.stackConfigProps.resources.s3UploaderLambdaRole.roleName}-${envName}-uploader-lr`,
      assumedBy: new iam.ServicePrincipal("lambda.amazonaws.com"),
      path: "/",
      inlinePolicies: {
        S3UploaderPolicy: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                "s3:GetObject",
                "s3:PutObject",
                "s3:CopyObject",
                "s3:DeleteObject",
                "s3:ListBucket"
              ],
              resources: [
                `arn:aws:s3:::${props.config.s3.distributionBucketName}`,
                `arn:aws:s3:::${props.config.s3.distributionBucketName}/*`,
                `arn:aws:s3:::${props.config.s3.webfocusBucketName}/*`,
                `arn:aws:s3:::${props.config.lambda.logging.extensionLogDestinationBucketName}`,
                `arn:aws:s3:::${props.config.lambda.logging.extensionLogDestinationBucketName}/*`
              ]
            })
          ]
        }),
        WriteLogPolicy: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:PutLogEvents"
              ],
              resources: ["arn:aws:logs:*:*:/aws/lambda/*"]
            })
          ]
        }),
        CloudwatchAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: ["cloudwatch:PutMetricData"],
              resources: ["*"]
            })
          ]
        }),
        NetworkInterface: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface"
              ],
              resources: ["*"]
            })
          ]
        }),
        Kinesis: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: ["kinesis:*"],
              resources: ["*"]
            })
          ]
        }),
        SNSPublish: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: ["sns:Publish"],
              resources: [props.errorNotifyTopicArn]
            })
          ]
        })
      }
    });

    // Add resource-specific tags to IAM role
    addResourceLevelTags(this.role, "iam", `${props.stackConfigProps.resources.s3UploaderLambdaRole.roleName}-${envName}-uploader-lr`);

    // Create Lambda using SharedLambdaConstruct pattern with custom role
    const lambdaConstruct = new SharedLambdaConstruct(this, "s3UploaderLambdaFunction", {
      environment: envName,
      vpc: props.vpc,
      securityGroup: props.securityGroup,
      functionName: functionName,
      logging: { retentionDays: props.config.lambda.logging.retentionDays },
      memorySize: props.config.lambda.memorySize,
      timeout: props.config.lambda.timeout,
      handlerFilePath: path.join(__dirname, "../src/s3-uploader.ts"),
      handlerFunctionName: "handler",
      description: "S3 Uploader Lambda for WebFocus Common Resources",
      region: props.config.region,
      role: this.role,  // Use the custom IAM role
      envVariables: {
        // Environment variables from configuration
        DISTRIBUTION_BUCKET_NAME: props.config.s3.distributionBucketName,
        VENDOR_GENERATING_FILE_PATH: props.config.filePaths.vendorGeneratingPath,
        VENDOR_UPLOADING_FILE_PATH: props.config.filePaths.vendorUploadingPath,
        WO_GENERATING_FILE_PATH: props.config.filePaths.woGeneratingPath,
        WO_UPLOADING_FILE_PATH: props.config.filePaths.woUploadingPath,
        PO_GENERATING_FILE_PATH: props.config.filePaths.poGeneratingPath,
        PO_UPLOADING_FILE_PATH: props.config.filePaths.poUploadingPath,
        TASK_CARD_GENERATING_FILE_PATH: props.config.filePaths.taskCardGeneratingPath,
        TASK_CARD_UPLOADING_FILE_PATH: props.config.filePaths.taskCardUploadingPath,
        ENGINE_DATA_GENERATING_FILE_PATH: props.config.filePaths.engineDataGeneratingPath,
        ENGINE_DATA_UPLOADING_FILE_PATH: props.config.filePaths.engineDataUploadingPath,
        SMC_WO_DATA_GENERATING_FILE_PATH: props.config.filePaths.smcWoDataGeneratingPath,
        SMC_WO_DATA_UPLOADING_FILE_PATH: props.config.filePaths.smcWoDataUploadingPath,
        PENDING_FILE_GENERATING_PATH: props.config.filePaths.pendingPath,
        PROCESSED_FILE_GENERATING_PATH: props.config.filePaths.processedPath,
        WEBFOCUS_S3_BUCKET: props.config.s3.webfocusBucketName,
        SNS_TOPIC_ARN: props.errorNotifyTopicArn
      }
    });

    this.function = lambdaConstruct.function;

    // Add resource-specific tags to Lambda function
    addResourceLevelTags(this.function, "lambda", functionName);

    // Lambda function now uses the custom IAM role with all required permissions
    // No need to add additional permissions since they're all included in the custom role
  }
}
