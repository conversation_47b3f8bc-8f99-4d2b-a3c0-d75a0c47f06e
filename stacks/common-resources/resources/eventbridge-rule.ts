import * as events from "aws-cdk-lib/aws-events";
import * as targets from "aws-cdk-lib/aws-events-targets";
import * as lambda from "aws-cdk-lib/aws-lambda";
import { StackProps } from "aws-cdk-lib";
import { Construct } from "constructs";

import {
  AppConfig,
  CommonResourceStackConfig,
} from "../../../configs/config.interface";
import { addResourceLevelTags } from "../../../utils/cdk-tagging";

export interface EventBridgeRuleStackProps extends StackProps {
  config: AppConfig;
  stackConfigProps: CommonResourceStackConfig;
  targetLambda: lambda.IFunction;
}

export class EventBridgeRuleStack extends Construct {
  public readonly rule: events.Rule;

  constructor(scope: Construct, id: string, props: EventBridgeRuleStackProps) {
    super(scope, id);

    const envName = props.config.environment;

    // Create EventBridge Rule (matching serverless.yml S3UploaderLambdaRule)
    this.rule = new events.Rule(this, "S3UploaderLambdaRule", {
      ruleName: `${props.stackConfigProps.resources.eventBridgeRule.ruleName}-${envName}`,
      description: "EventBridge rule to trigger S3 Uploader Lambda function when an object is created",
      eventPattern: {
        source: ["aws.s3"],
        detailType: ["Object Created"],
        detail: {
          bucket: {
            name: [props.config.s3.distributionBucketName]
          },
          object: {
            key: [
              { prefix: `${props.config.filePaths.vendorGeneratingPath}${props.config.filePaths.pendingPath}` },
              { prefix: `${props.config.filePaths.woGeneratingPath}${props.config.filePaths.pendingPath}` },
              { prefix: `${props.config.filePaths.poGeneratingPath}${props.config.filePaths.pendingPath}` },
              { prefix: `${props.config.filePaths.taskCardGeneratingPath}${props.config.filePaths.pendingPath}` },
              { prefix: `${props.config.filePaths.engineDataGeneratingPath}${props.config.filePaths.pendingPath}` },
              { prefix: `${props.config.filePaths.smcWoDataGeneratingPath}${props.config.filePaths.pendingPath}` }
            ]
          }
        }
      },
      enabled: true
    });

    // Add Lambda as target to EventBridge rule
    // The LambdaFunction target automatically creates the necessary invoke permission
    this.rule.addTarget(new targets.LambdaFunction(props.targetLambda, {
      // Optional: specify retry policy or other target properties
    }));

    // Add resource-specific tags
    addResourceLevelTags(this.rule, "events", `${props.stackConfigProps.resources.eventBridgeRule.ruleName}-${envName}`);
  }
}
