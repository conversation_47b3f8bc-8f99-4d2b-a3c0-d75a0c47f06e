import * as cdk from "aws-cdk-lib";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import { Construct } from "constructs";
import {
  AppConfig,
  DefaultStackSynthesizer,
  CommonResourceStackConfig,
} from "../../utils/config-util";

import { S3UploaderLambdaStack } from "./resources/s3-uploader-lambda";
import { ErrorNotificationStack } from "./resources/error-notification";
import { EventBridgeRuleStack } from "./resources/eventbridge-rule";

export interface CommonResourceStackProps extends cdk.StackProps {
  config: AppConfig;
  synthesizer: DefaultStackSynthesizer;
  stackConfigProps: CommonResourceStackConfig;
}

export class CommonResourceStack extends cdk.Stack {
  public readonly s3UploaderLambda: any;
  public readonly errorNotifyTopic: any;
  public readonly eventBridgeRule: any;

  constructor(scope: Construct, id: string, props: CommonResourceStackProps) {
    super(scope, id, {
      env: {
        account: props.config.account,
        region: props.config.region,
      },
      stackName: `${props.stackConfigProps.stackName}-${props.config.environment}`,
      ...props,
      synthesizer: props.synthesizer,
    });

    const vpc = ec2.Vpc.fromLookup(this, "VPC", {
      vpcId: props.config.vpc.vpcId,
      isDefault: false,
    });

    const securityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      "sgIdDigitalOdsRdsCredentials",
      props.config.vpc.securityGroupId
    );

    const errorNotification = new ErrorNotificationStack(
      this,
      "ErrorNotification",
      {
        config: props.config,
        stackConfigProps: props.stackConfigProps,
      }
    );
    this.errorNotifyTopic = errorNotification.topic;

    const s3UploaderLambda = new S3UploaderLambdaStack(
      this,
      "S3UploaderLambda",
      {
        vpc,
        securityGroup,
        config: props.config,
        stackConfigProps: props.stackConfigProps,
        errorNotifyTopicArn: this.errorNotifyTopic.topicArn,
      }
    );
    this.s3UploaderLambda = s3UploaderLambda.function;

    const eventBridgeRule = new EventBridgeRuleStack(this, "EventBridgeRule", {
      config: props.config,
      stackConfigProps: props.stackConfigProps,
      targetLambda: this.s3UploaderLambda,
    });
    this.eventBridgeRule = eventBridgeRule.rule;

    new cdk.CfnOutput(this, "ErrorNotifyTopicArn", {
      value: this.errorNotifyTopic.topicArn,
      exportName: "error-notify-topic-arn",
      description: "ARN of the error notification SNS topic",
    });
  }
}
