import sys
import os
import logging
import json
import boto3
from datetime import datetime
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from pyspark.context import SparkContext
from awsglue.context import GlueContext
from awsglue.job import Job
from awsglue.dynamicframe import DynamicFrame
# Import glue-utils
from glue_utils.core.glue_context_wrapper import GlueContextWrapper

if "pytest" in sys.modules:
    sys.argv = [
        "",
        "--JOB_NAME=initial-load-extraction-job",
        "--DISTRIBUTION_BUCKET_NAME=mocktest",
        "--REGION=mocktest",
        "--ACCOUNT_ID=mocktest"
    ]

#initialize jobs and context
args = getResolvedOptions(sys.argv, [
    "JOB_NAME",
    "DISTRIBUTION_BUCKET_NAME",
    "REGION",
    "ACCOUNT_ID",
    "TRAX_DB_SECRET",
    "START_DATE",
    "END_DATE",
    "DISTRIBUTION_FOLDER_PATH_WO",
    "FILE_PREFIX_WO",
    "DISTRIBUTION_FOLDER_PATH_VENDOR",
    "FILE_PREFIX_VENDOR",
    "DISTRIBUTION_FOLDER_PATH_PO",
    "FILE_PREFIX_PO",
    "DISTRIBUTION_FOLDER_PATH_TASK_CARD",
    "FILE_PREFIX_TASK_CARD",
    "DISTRIBUTION_FOLDER_PATH_ENGINE",
    "FILE_PREFIX_ENGINE_DATA",
    "SNS_TOPIC_ARN",
    "PENDING_FILE_GENERATING_PATH"
])
# Initialize GlueContextWrapper instead of standard context
glue_wrapper = GlueContextWrapper()
spark = glue_wrapper.spark
sc = spark.sparkContext
glueContext = GlueContext(sc)
job = Job(glueContext)

JOB_NAME = args['JOB_NAME']
JOB_RUN_ID = args['JOB_RUN_ID']
DISTRIBUTION_BUCKET_NAME = args['DISTRIBUTION_BUCKET_NAME']
REGION = args['REGION']
ACCOUNT_ID = args['ACCOUNT_ID']
TRAX_DB_SECRET = args['TRAX_DB_SECRET']
START_DATE = args['START_DATE']
END_DATE = args['END_DATE']
DISTRIBUTION_FOLDER_PATH_WO = args['DISTRIBUTION_FOLDER_PATH_WO']
FILE_PREFIX_WO = args['FILE_PREFIX_WO']
DISTRIBUTION_FOLDER_PATH_VENDOR = args['DISTRIBUTION_FOLDER_PATH_VENDOR']
FILE_PREFIX_VENDOR = args['FILE_PREFIX_VENDOR']
DISTRIBUTION_FOLDER_PATH_PO = args['DISTRIBUTION_FOLDER_PATH_PO']
FILE_PREFIX_PO = args['FILE_PREFIX_PO']
DISTRIBUTION_FOLDER_PATH_TASK_CARD = args['DISTRIBUTION_FOLDER_PATH_TASK_CARD']
FILE_PREFIX_TASK_CARD = args['FILE_PREFIX_TASK_CARD']
DISTRIBUTION_FOLDER_PATH_ENGINE = args['DISTRIBUTION_FOLDER_PATH_ENGINE']
FILE_PREFIX_ENGINE_DATA = args['FILE_PREFIX_ENGINE_DATA']
SNS_TOPIC_ARN = args['SNS_TOPIC_ARN']
PENDING_FILE_GENERATING_PATH = args['PENDING_FILE_GENERATING_PATH']
#Config logging for system
logging.basicConfig(level=os.environ.get("LOGLEVEL", "INFO"),
    format='[%(filename)s:%(lineno)s - %(funcName)10s() ] %(asctime)-15s %(message)s',
    datefmt='%Y-%m-%d:%H:%M:%S')
logger = logging.getLogger(__name__)

def send_error_to_sns(ex):
    sns = boto3.client('sns', region_name=REGION)
    sns.publish(
        TopicArn=SNS_TOPIC_ARN,
        Message=str(ex),
        Subject=f'Error From Glue Job {JOB_NAME}'
    )

def get_trax_db_credentials():
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name=REGION);
    response = client.get_secret_value(SecretId=TRAX_DB_SECRET)
    secretString = response['SecretString']
    secret = json.loads(secretString)
    jdbc_username = secret['username']
    jdbc_password = secret['password']
    host = secret['connectString']
    jdbc_url = f"jdbc:oracle:thin:@{host}"

    return jdbc_username, jdbc_password, jdbc_url

def get_yearly_date_pairs(start_date_str, end_date_str):
    # Parse input dates
    start_date = datetime.strptime(start_date_str, '%Y%m%d')
    end_date = datetime.now()  # Use current date instead of passed parameter
    
    result = []
    current_year = start_date.year
    
    while current_year <= end_date.year:
        # Determine start date for current year
        if current_year == start_date.year:
            year_start = start_date.strftime('%Y-%m-%d')
        else:
            year_start = f"{current_year}-01-01"
        
        # Determine end date for current year
        if current_year == end_date.year:
            year_end = end_date.strftime('%Y-%m-%d')
        else:
            year_end = f"{current_year}-12-31"
        
        result.append({
            'start_date': year_start,
            'end_date': year_end
        })
        
        current_year += 1
    
    return result

def renamePySparkS3FileName(bucketName, folderPath, newFileName, folder_path_pending):
    #Rename s3 file to specific custom name    
    client = boto3.client('s3')
    #getting all the content/file inside the bucket specific folder
    response = client.list_objects_v2(Bucket=bucketName, Prefix=folderPath)
    names = response["Contents"]
    
    #Find out the file which have part-000* in it's Key
    particulars = [name['Key'] for name in names if 'part-000' in name['Key']]
    
    #Find out the prefix of part-000* because we want to retain the partitions schema 
    location = [particular.split('part-000')[0] for particular in particulars]
    
    #copy object with custom name and delete old object
    for key,particular in enumerate(particulars):
        client.copy_object(Bucket=bucketName, CopySource=bucketName + "/" + particular, Key=folder_path_pending + newFileName)
        client.delete_object(Bucket=bucketName, Key=particular)

def put_file_to_s3(df, folder_path,file_prefix,start_date,end_date):
    folder_path_temp = folder_path + 'temp'
    folder_path_pending = folder_path + PENDING_FILE_GENERATING_PATH
    file_path = f"s3://{DISTRIBUTION_BUCKET_NAME}/{folder_path_temp}/"
    glue_wrapper.write_data(
        dataframe=df.coalesce(1),
        target_type="S3",
        connection_options={
            "path": file_path,
            "format": "csv",
            "delimiter": ",",
            "header": "true",
            "escape": "\"",
        },
        mode="append"
    )
    current_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if start_date is None and end_date is None:
        file_name = f"{file_prefix}_{current_timestamp}.csv"
    else:
        file_name = f"{file_prefix}_{start_date}_{end_date}_{current_timestamp}.csv"    
    
    renamePySparkS3FileName(DISTRIBUTION_BUCKET_NAME, folder_path_temp, file_name, folder_path_pending)

def get_vendor_data(jdbc_url, jdbc_username, jdbc_password):
    try:
        logger.info(f"Started getting vendor data")

        df_vendor = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "(SELECT * FROM ODB.RELATION_MASTER WHERE RELATION_TRANSACTION='VENDOR') filtered_data",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )
        df_vendor.createOrReplaceTempView("RELATION_MASTER")

        sql_query = """
        SELECT 
            RELATION_CODE, 
            NAME,
            MAIL_EMAIL,
            MAIL_PHONE,
            MAIL_ADDRESS_1,
            MAIL_ADDRESS_2,
            MAIL_CITY,
            MAIL_STATE,
            MAIL_COUNTRY,
            MAIL_POST
        FROM RELATION_MASTER
        """

        finalDf = glue_wrapper.execute_spark_sql(sql_query)
        finalDf.cache() # Cache the result for better performance

        if not finalDf.isEmpty():
            put_file_to_s3(finalDf, DISTRIBUTION_FOLDER_PATH_VENDOR, FILE_PREFIX_VENDOR, None, None)
            logger.info("Vendor data written to S3 successfully")
        else:
            logger.info('No Vendor records to write') 
    except Exception as e:
        logger.error(f"Error getting vendor data: {e}")
        raise e

def get_wo_po_data(start_date, end_date):
    try:
        logger.info(f"Started getting WO and PO data from {start_date} to {end_date}")

        # Process WO data with enhanced query based on provided Oracle SQL
        wo_sql_query = f"""
        WITH WO_INIT AS (
            SELECT 
                WO.WO, 
                WO.PROJECT, 
                WO.EXTERNAL_REFERENCE, 
                WO.AC, 
                WO.WO_CATEGORY, 
                WO.PRIORITY, 
                WO.STATUS, 
                WO.SCHEDULE_ORG_COMPLETION_DATE, 
                WO.SCHEDULE_ORG_COMPLETION_HOUR, 
                WO.SCHEDULE_ORG_COMPLETION_MINUTE, 
                WO.SCHEDULE_COMPLETION_DATE, 
                WO.SCHEDULE_COMPLETION_HOUR, 
                WO.SCHEDULE_COMPLETION_MINUTE, 
                WO.SCHEDULE_START_DATE, 
                WO.SCHEDULE_START_HOUR, 
                WO.SCHEDULE_START_MINUTE, 
                WO.ACTUAL_START_DATE, 
                WO.ACTUAL_START_HOUR, 
                WO.ACTUAL_START_MINUTE, 
                WO.LOCATION, 
                REPLACE(
                    REPLACE(WO.WO_DESCRIPTION, '"', ' '), 
                    '&', 
                    'and'
                ) AS DESCRIPTION, 
                WO.VENDOR, 
                WO.SITE,
                WO.WO_DESCRIPTION, 
                WO.CREATED_DATE, 
                WO.CREATED_BY, 
                WO.MODIFIED_DATE, 
                WO.MODIFIED_BY 
            FROM 
                WO 
            WHERE 
                (
                    WO.CREATED_BY = 'TRAXIFACE' 
                    OR WO.PROJECT = 'EMS'
                ) 
                AND WO.CREATED_DATE >= to_date('{start_date}', 'yyyy-MM-dd') 
                AND WO.CREATED_DATE <= to_date('{end_date}', 'yyyy-MM-dd')
        ), 
        WO_INFO AS (
            SELECT 
                WO.WO, 
                WO.AC, 
                CASE 
                    WHEN WO.PROJECT = 'EMS' 
                    AND WO.EXTERNAL_REFERENCE IS NULL THEN concat(WO.WO, '_', WO.AC) 
                    ELSE trim(substring(WO.EXTERNAL_REFERENCE, 1, 10))
                END AS EVENT_ID, 
                CASE 
                    WHEN WO.PROJECT = 'EMS' THEN 'EMS' 
                    ELSE (
                        CASE 
                            WHEN WO.WO_CATEGORY = 'HMV' 
                            AND WO.PRIORITY = 'LOW' THEN 'HML' 
                            ELSE (
                                CASE 
                                    WHEN WO.WO_CATEGORY IN ('OOS', 'PRK') THEN WO.WO_CATEGORY 
                                END
                            ) 
                        END
                    ) 
                END AS EVENT_TYPE,
                
                CASE 
                    WHEN WO.STATUS IN ('OPEN', 'COMPLETED') THEN concat(
                        date_format(WO.SCHEDULE_ORG_COMPLETION_DATE, 'yyyy-MM-dd'), 
                        ' ', 
                        trim(
                            substring(
                                concat(
                                    lpad(cast(WO.SCHEDULE_ORG_COMPLETION_HOUR as string), 2, '0'), 
                                    ':', 
                                    lpad(cast(WO.SCHEDULE_ORG_COMPLETION_MINUTE as string), 2, '0')
                                ), 
                                1, 
                                8
                            )
                        )
                    ) 
                    ELSE concat(
                        date_format(WO.SCHEDULE_COMPLETION_DATE, 'yyyy-MM-dd'), 
                        ' ', 
                        trim(
                            substring(
                                concat(
                                    lpad(cast(WO.SCHEDULE_COMPLETION_HOUR as string), 2, '0'), 
                                    ':', 
                                    lpad(cast(WO.SCHEDULE_COMPLETION_MINUTE as string), 2, '0')
                                ), 
                                1, 
                                8
                            )
                        )
                    ) 
                END AS ACTUAL_COMPLETION_DATETIME, 
                CASE 
                    WHEN date_format(current_date(), 'yyyy-MM-dd') < date_format(
                        date_sub(WO.ACTUAL_START_DATE, 35), 'yyyy-MM-dd'
                    ) 
                    AND WO.STATUS = 'OPEN' THEN 5 
                    WHEN date_format(current_date(), 'yyyy-MM-dd') >= date_format(
                        date_sub(WO.ACTUAL_START_DATE, 35), 'yyyy-MM-dd'
                    ) 
                    AND date_format(current_date(), 'yyyy-MM-dd') < date_format(
                        date_sub(WO.ACTUAL_START_DATE, 10), 'yyyy-MM-dd'
                    ) 
                    AND WO.STATUS = 'OPEN' THEN 4 
                    WHEN date_format(current_date(), 'yyyy-MM-dd') >= date_format(
                        date_sub(WO.ACTUAL_START_DATE, 10), 'yyyy-MM-dd'
                    ) 
                    AND date_format(current_date(), 'yyyy-MM-dd') < date_format(
                        WO.ACTUAL_START_DATE, 'yyyy-MM-dd'
                    ) 
                    AND WO.STATUS = 'OPEN' THEN 3 
                    WHEN date_format(current_date(), 'yyyy-MM-dd') >= date_format(
                        WO.ACTUAL_START_DATE, 'yyyy-MM-dd'
                    ) 
                    AND WO.STATUS = 'OPEN' THEN 2 
                    WHEN WO.STATUS IN (
                        'POSTCOMPLT', 'COMPLETED', 'CLOSED'
                    ) THEN 1 
                END AS CHECK_STATUS, 
                WO.SCHEDULE_START_DATE, 
                concat(
                    date_format(WO.ACTUAL_START_DATE, 'yyyy-MM-dd'), 
                    ' ', 
                    trim(
                        substring(
                            concat(
                                lpad(cast(WO.ACTUAL_START_HOUR as string), 2, '0'), 
                                ':', 
                                lpad(cast(WO.ACTUAL_START_MINUTE as string), 2, '0')
                            ), 
                            1, 
                            8
                        )
                    )
                ) AS ACTUAL_START_DATETIME, 
                WO.LOCATION, 
                CASE 
                    WHEN locate(',', WO.WO_DESCRIPTION) <> 0 THEN substring(
                        substring(
                            WO.WO_DESCRIPTION, 
                            locate(',', WO.WO_DESCRIPTION) + 1, 
                            locate(',', WO.WO_DESCRIPTION, locate(',', WO.WO_DESCRIPTION) + 1) - locate(',', WO.WO_DESCRIPTION)
                        ), 
                        2, 
                        30
                    ) 
                    ELSE substring(WO.WO_DESCRIPTION, 1, 55) 
                END AS DESCRIPTION, 
                WO.VENDOR, 
                WO.SCHEDULE_COMPLETION_DATE, 
                WO.SCHEDULE_COMPLETION_HOUR, 
                WO.SCHEDULE_COMPLETION_MINUTE, 
                WO.SCHEDULE_ORG_COMPLETION_DATE, 
                WO.SCHEDULE_ORG_COMPLETION_HOUR, 
                WO.SCHEDULE_ORG_COMPLETION_MINUTE, 
                WO.SCHEDULE_START_HOUR, 
                WO.SCHEDULE_START_MINUTE, 
                WO.SITE, 
                WO.WO_DESCRIPTION, 
                WO.PROJECT, 
                WO.WO_CATEGORY, 
                WO.PRIORITY, 
                WO.EXTERNAL_REFERENCE, 
                WO.STATUS, 
                WO.CREATED_DATE, 
                WO.CREATED_BY, 
                WO.MODIFIED_DATE, 
                WO.MODIFIED_BY 
            FROM 
                WO_INIT WO
        ), 
        TASK_COUNTS AS (
            SELECT 
                WOTC.WO, 
                COUNT(
                    CASE 
                        WHEN coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD 
                    END
                ) AS TOTAL_TC, 
                COUNT(
                    CASE 
                        WHEN WOTC.STATUS = 'CANCEL' 
                        AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD 
                    END
                ) AS TOTAL_CANCEL_TC, 
                COUNT(
                    CASE 
                        WHEN WOTC.TASK_CARD NOT LIKE 'NR-%' 
                        AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD 
                    END
                ) AS TOTAL_ROUTINE_TC, 
                COUNT(
                    CASE 
                        WHEN WOTC.TASK_CARD NOT LIKE 'NR-%' 
                        AND WOTC.STATUS = 'OPEN' 
                        AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD 
                    END
                ) AS OPEN_ROUTINE_TC, 
                COUNT(
                    CASE 
                        WHEN WOTC.TASK_CARD NOT LIKE 'NR-%' 
                        AND WOTC.STATUS = 'CLOSED' 
                        AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD 
                    END
                ) AS CLOSED_ROUTINE_TC, 
                COUNT(
                    CASE 
                        WHEN WOTC.TASK_CARD NOT LIKE 'NR-%' 
                        AND WOTC.STATUS = 'CANCEL' 
                        AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD 
                    END
                ) AS CANCEL_ROUTINE_TC, 
                COUNT(
                    CASE 
                        WHEN WOTC.TASK_CARD LIKE 'NR-%' 
                        AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD 
                    END
                ) AS TOTAL_NON_ROUTINE_TC, 
                COUNT(
                    CASE 
                        WHEN WOTC.TASK_CARD LIKE 'NR-%' 
                        AND WOTC.STATUS = 'OPEN' 
                        AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD 
                    END
                ) AS OPEN_NON_ROUTINE_TC, 
                COUNT(
                    CASE 
                        WHEN WOTC.TASK_CARD LIKE 'NR-%' 
                        AND WOTC.STATUS = 'CLOSED' 
                        AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD 
                    END
                ) AS CLOSED_NON_ROUTINE_TC, 
                COUNT(
                    CASE 
                        WHEN WOTC.TASK_CARD LIKE 'NR-%' 
                        AND WOTC.STATUS = 'CANCEL' 
                        AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD 
                    END
                ) AS CANCEL_NON_ROUTINE_TC, 
                SUM(
                    CASE 
                        WHEN WOTC.STATUS IN ('CLOSED', 'CANCEL') 
                        AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN 1 
                        ELSE 0 
                    END
                ) AS COMPLETED_OR_CANCELED_TC 
            FROM 
                WO_TASK_CARD WOTC 
            WHERE 
                WOTC.WO IN (
                    SELECT 
                        WO 
                    FROM 
                        WO_INFO
                ) 
            GROUP BY 
                WOTC.WO
        ), 
        MAN_HOURS AS (
            SELECT 
                WOTC.WO, 
                coalesce(
                    SUM(
                        CASE 
                            WHEN VCT.MAN_HOURS <> 0 THEN VCT.MAN_HOURS 
                        END
                    ), 
                    0
                ) AS TOTAL_MH, 
                coalesce(
                    SUM(
                        CASE 
                            WHEN WOTC.STATUS <> 'OPEN' 
                            AND VCT.MAN_HOURS <> 0 THEN VCT.MAN_HOURS 
                        END
                    ), 
                    0
                ) AS TOTAL_MH_COMPLETED 
            FROM 
                WO_TASK_CARD WOTC 
                INNER JOIN VENDOR_CONTRACT_TASK VCT ON WOTC.TASK_CARD = VCT.TASK_CARD 
            WHERE 
                WOTC.WO IN (
                    SELECT 
                        WO 
                    FROM 
                        WO_INFO
                ) 
            GROUP BY 
                WOTC.WO
        ), 
        LATEST_FLIGHT AS (
            SELECT 
                AF.AC, 
                AF.FLIGHT, 
                AF.ORIGIN, 
                AF.DESTINATION, 
                AF.FLIGHT_DATE, 
                AF.ON_HOUR, 
                AF.ON_MINUTE 
            FROM 
                AC_ACTUAL_FLIGHTS AF 
            WHERE 
                (AF.AC, AF.CREATED_DATE) IN (
                    SELECT 
                        AC, 
                        MAX(CREATED_DATE) 
                    FROM 
                        AC_ACTUAL_FLIGHTS 
                    WHERE 
                        AC IN (
                            SELECT 
                                AC 
                            FROM 
                                WO_INFO
                        ) 
                    GROUP BY 
                        AC
                )
        ) 
        SELECT 
            WO.WO, 
            WO.EVENT_ID,
            WO.EVENT_TYPE,
            CASE
                WHEN TRIM(WO.WO_DESCRIPTION) LIKE '%AOG%'  THEN 'AOG'
                WHEN TRIM(WO.WO_DESCRIPTION) LIKE 'EXIT%'  THEN 'EXIT'
                WHEN TRIM(WO.WO_DESCRIPTION) LIKE 'STC%'   THEN 'STC'
                WHEN TRIM(WO.WO_DESCRIPTION) LIKE '%RTS%'  THEN 'RTS'
                ELSE WO.EVENT_TYPE
            END AS EVENT_REPORT_TYPE, 
            WO.CHECK_STATUS, 
            (unix_timestamp(to_timestamp(WO.ACTUAL_COMPLETION_DATETIME, 'yyyy-MM-dd HH:mm')) - 
            unix_timestamp(to_timestamp(WO.ACTUAL_START_DATETIME, 'yyyy-MM-dd HH:mm'))) / 86400.0 AS DURATION,
            WO.ACTUAL_START_DATETIME, 
            CASE 
                WHEN TC.TOTAL_TC = 0 
                OR TC.COMPLETED_OR_CANCELED_TC = 0 THEN 0 
                WHEN WO.SCHEDULE_START_DATE > current_date() THEN 0 
                ELSE round(TC.COMPLETED_OR_CANCELED_TC / TC.TOTAL_TC * 100) 
            END AS PERCENT_COMPLETED, 
            TC.TOTAL_TC, 
            TC.TOTAL_CANCEL_TC, 
            TC.TOTAL_ROUTINE_TC, 
            TC.OPEN_ROUTINE_TC, 
            TC.CLOSED_ROUTINE_TC, 
            TC.CANCEL_ROUTINE_TC, 
            TC.TOTAL_NON_ROUTINE_TC, 
            TC.OPEN_NON_ROUTINE_TC, 
            TC.CLOSED_NON_ROUTINE_TC, 
            TC.CANCEL_NON_ROUTINE_TC, 
            MH.TOTAL_MH, 
            MH.TOTAL_MH_COMPLETED, 
            CASE 
                WHEN MH.TOTAL_MH = 0 THEN 0 
                ELSE round(MH.TOTAL_MH_COMPLETED / MH.TOTAL_MH * 100) 
            END AS MH_ROUTINE_CHECK_COMPLETION, 
            AC.AC_TYPE AS TYPE, 
            AC.AC_SN, 
            AC.AC_FLIGHT_HOURS, 
            AC.AC_FLIGHT_MINUTES, 
            AC.AC_CYCLES, 
            AC.LAST_AC_REGISTRATION, 
            substring(AC.BASIC_NUMBER, 1, 2) AS OPERATOR, 
            LF.AC,
            LF.FLIGHT, 
            LF.ORIGIN, 
            LF.DESTINATION, 
            LF.FLIGHT_DATE, 
            LF.ON_HOUR, 
            LF.ON_MINUTE, 
            WO.LOCATION, 
            WO.DESCRIPTION, 
            WO.VENDOR, 
            WO.SCHEDULE_START_DATE,
            WO.SCHEDULE_START_HOUR, 
            WO.SCHEDULE_START_MINUTE, 
            WO.SCHEDULE_COMPLETION_DATE, 
            WO.SCHEDULE_COMPLETION_HOUR, 
            WO.SCHEDULE_COMPLETION_MINUTE, 
            WO.SCHEDULE_ORG_COMPLETION_DATE, 
            WO.SCHEDULE_ORG_COMPLETION_HOUR, 
            WO.SCHEDULE_ORG_COMPLETION_MINUTE, 
            WO.SITE, 
            WO.PROJECT, 
            WO.WO_CATEGORY, 
            WO.PRIORITY, 
            WO.EXTERNAL_REFERENCE, 
            WO.STATUS, 
            WO.CREATED_DATE, 
            WO.CREATED_BY, 
            WO.MODIFIED_DATE, 
            WO.MODIFIED_BY, 
            LM.TIME_ZONE_NAME 
        FROM 
            WO_INFO WO 
            LEFT JOIN TASK_COUNTS TC ON WO.WO = TC.WO 
            LEFT JOIN MAN_HOURS MH ON WO.WO = MH.WO 
            LEFT JOIN AC_MASTER AC ON WO.AC = AC.AC 
            LEFT JOIN LATEST_FLIGHT LF ON WO.AC = LF.AC 
            LEFT JOIN LOCATION_MASTER LM ON WO.LOCATION = LM.LOCATION 
        ORDER BY 
            WO.SCHEDULE_START_DATE
        """

        logger.info(f"Started executing WO query for {start_date} to {end_date}")
        wo_finalDf = glue_wrapper.execute_spark_sql(wo_sql_query)
        wo_finalDf.cache()

        if not wo_finalDf.isEmpty():
            put_file_to_s3(wo_finalDf, DISTRIBUTION_FOLDER_PATH_WO, FILE_PREFIX_WO, start_date, end_date)
            logger.info(f"WO data written to S3 successfully for {start_date} to {end_date}")
        else:
            logger.info('No WO records to write')
    except Exception as e:
        logger.error(f"Error in get_wo_po_data: {e}")
        raise e
    
    try:

        # Process PO data
        po_sql_query = f"""
        SELECT
            WO.WO,
            WO.EXTERNAL_REFERENCE,
            VC.CURRENCY AS CURRENCY_CODE,
            RV.RELATION_CODE AS SUPPLIER_CODE,
            RV.MAIL_ADDRESS_1 AS SUPPLIER_ADDRESS1,
            RV.MAIL_ADDRESS_2 AS SUPPLIER_ADDRESS2,
            RV.MAIL_CITY AS SUPPLIER_CITY,
            RV.MAIL_POST AS SUPPLIER_POST,
            RV.MAIL_STATE AS SUPPLIER_STATE,
            RV.MAIL_COUNTRY AS SUPPLIER_COUNTRY,
            RV.MAIL_PHONE AS SUPPLIER_PHONE,
            RV.MAIL_FAX AS SUPPLIER_FAX,
            RV.MAIL_CELL AS SUPPLIER_CELL,
            RV.MAIL_EMAIL AS SUPPLIER_EMAIL,
            SUBSTR(COALESCE(RV.NAME, ' - NO SUPPLIER - '), 1, 40) AS SUPPLIER
        FROM WO
        LEFT JOIN WO_VENDOR_CONTRACT WVC ON WO.WO = WVC.WO
        LEFT JOIN VENDOR_CONTRACT VC ON WO.VENDOR = VC.VENDOR 
            AND WO.LOCATION = VC.LOCATION 
            AND WVC.CONTRACT_TYPE = VC.CONTRACT_TYPE
        LEFT JOIN RELATION_MASTER RV ON WO.VENDOR = RV.RELATION_CODE 
            AND COALESCE(RV.RELATION_TRANSACTION, 'VENDOR') = 'VENDOR'
        WHERE
            SUBSTR(WO.LOCATION, 4, 4) IN ('1','2','3','4','5','6','7','8','9')
            AND WO.STATUS IN ('COMPLETED', 'POSTCOMPLT', 'OPEN', 'CLOSED')
            AND (WO.CREATED_BY = 'TRAXIFACE' OR WO.PROJECT = 'EMS')
            AND WO.CREATED_DATE >= to_date('{start_date}', 'yyyy-MM-dd')
            AND WO.CREATED_DATE <= to_date('{end_date}', 'yyyy-MM-dd')
        """

        logger.info(f"Started executing PO query for {start_date} to {end_date}")
        po_finalDf = glue_wrapper.execute_spark_sql(po_sql_query)
        po_finalDf.cache()

        if not po_finalDf.isEmpty():
            put_file_to_s3(po_finalDf, DISTRIBUTION_FOLDER_PATH_PO, FILE_PREFIX_PO, start_date, end_date)
            logger.info(f"PO data written to S3 successfully for {start_date} to {end_date}")
        else:
            logger.info('No PO records to write')
    except Exception as e:
        logger.error(f"Error in get_wo_po_data: {e}")
        raise e

    

def get_tcc_data():
    try:
        # Process TCC data
        tcc_sql_query = """
        WITH WO_INFO AS (
        SELECT 
            WO.WO, 
            CASE WHEN PROJECT = 'EMS' THEN CONCAT(WO.WO, '_', WO.AC) 
                ELSE TRIM(SUBSTRING(WO.EXTERNAL_REFERENCE, 1, 10)) END AS EVENT_ID, 
            WO.SCHEDULE_START_DATE, 
            WO.SCHEDULE_START_DATE AS ACTUAL_START_DATE, 
            WO.SCHEDULE_COMPLETION_DATE 
        FROM 
            WO 
        WHERE 
            SUBSTRING(WO.LOCATION, 4, 4) IN (
            '1', '2', '3', '4', '5', '6', '7', '8', '9'
            ) 
            AND (
            WO.CREATED_BY = 'TRAXIFACE' 
            OR WO.PROJECT = 'EMS'
            ) 
            AND WO.STATUS IN ('POSTCOMPLT', 'OPEN') 
            AND WO.SCHEDULE_COMPLETION_DATE BETWEEN date_sub(current_date(), 365) AND date_add(current_date(), 365)
        ), 
        TC_INFO AS (
        SELECT 
            WOTC.WO, 
            WOTC.TASK_CARD, 
            CASE WHEN PID.INSTALLED_POSITION = 'ONLY' 
                THEN SUBSTRING(PN.PN_DESCRIPTION, 1, 3) 
                ELSE PID.INSTALLED_POSITION END AS POSITION, 
            WOTC.TASK_CARD_DESCRIPTION, 
            WOTC.STATUS, 
            WOTC.TASK_CARD_NUMBERING_SYSTEM, 
            WOTC.REVISION, 
            WOTC.PN, 
            WOTC.PN_SN, 
            TCI.SKILL 
        FROM 
            WO_INFO WO 
            LEFT JOIN WO_TASK_CARD WOTC ON WO.WO = WOTC.WO 
            LEFT JOIN PN_INVENTORY_DETAIL PID ON WOTC.PN = PID.PN 
            AND WOTC.PN_SN = PID.SN 
            LEFT JOIN PN_MASTER PN ON PID.PN = PN.PN
            CROSS JOIN TASK_CARD_ITEM TCI 
        WHERE 
            WOTC.TASK_CARD = TCI.TASK_CARD 
            AND TCI.TASK_CARD_ITEM = 1 
        UNION 
        SELECT 
            WOTC.WO, 
            WOTC.TASK_CARD, 
            NULL AS POSITION, 
            WOTC.TASK_CARD_DESCRIPTION, 
            WOTC.STATUS, 
            WOTC.TASK_CARD_NUMBERING_SYSTEM, 
            WOTC.REVISION, 
            WOTC.PN, 
            WOTC.PN_SN, 
            NULL AS SKILL 
        FROM 
            WO_INFO WO 
            LEFT JOIN WO_TASK_CARD WOTC ON WO.WO = WOTC.WO 
        WHERE 
            WOTC.TASK_CARD LIKE 'NR%' 
        ORDER BY 
            TASK_CARD_NUMBERING_SYSTEM
        ) 
        SELECT 
        WO.WO, 
        WO.EVENT_ID, 
        'T35' AS PERIOD, 
        WTC.TASK_CARD, 
        TRIM(WTC.PN) AS PN, 
        TRIM(WTC.PN_SN) AS PN_SN, 
        CONCAT(WO.EVENT_ID, '-T35') AS PID, 
        CONCAT(WO.EVENT_ID, '-', WTC.TASK_CARD, '-', TRIM(WTC.PN), '-', TRIM(WTC.PN_SN)) AS TID, 
        CONCAT(WO.EVENT_ID, '-T35-', WTC.TASK_CARD, '-', TRIM(WTC.PN), '-', TRIM(WTC.PN_SN)) AS UNIQUE_KEY 
        FROM 
        TC_INFO WTC 
        INNER JOIN WO_INFO WO ON WTC.WO = WO.WO 
        WHERE 
        date_format(current_date(), 'yyyy-MM-dd') <= date_format(
            date_sub(coalesce(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE), 35), 
            'yyyy-MM-dd'
        ) 
        UNION ALL 
        SELECT 
        WO.WO, 
        WO.EVENT_ID, 
        'T10' AS PERIOD, 
        WTC.TASK_CARD, 
        TRIM(WTC.PN) AS PN, 
        TRIM(WTC.PN_SN) AS PN_SN, 
        CONCAT(WO.EVENT_ID, '-T10') AS PID, 
        CONCAT(WO.EVENT_ID, '-', WTC.TASK_CARD, '-', TRIM(WTC.PN), '-', TRIM(WTC.PN_SN)) AS TID, 
        CONCAT(WO.EVENT_ID, '-T10-', WTC.TASK_CARD, '-', TRIM(WTC.PN), '-', TRIM(WTC.PN_SN)) AS UNIQUE_KEY 
        FROM 
        TC_INFO WTC 
        INNER JOIN WO_INFO WO ON WTC.WO = WO.WO 
        WHERE 
        date_format(current_date(), 'yyyy-MM-dd') <= date_format(
            date_sub(coalesce(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE), 35), 
            'yyyy-MM-dd'
        ) 
        OR (
            date_format(current_date(), 'yyyy-MM-dd') > date_format(
            date_sub(coalesce(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE), 35), 
            'yyyy-MM-dd'
            ) 
            AND date_format(current_date(), 'yyyy-MM-dd') <= date_format(
            date_sub(coalesce(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE), 10), 
            'yyyy-MM-dd'
            )
        ) 
        UNION ALL 
        SELECT 
        WO.WO, 
        WO.EVENT_ID, 
        'T00' AS PERIOD, 
        WTC.TASK_CARD, 
        TRIM(WTC.PN) AS PN, 
        TRIM(WTC.PN_SN) AS PN_SN, 
        CONCAT(WO.EVENT_ID, '-T00') AS PID, 
        CONCAT(WO.EVENT_ID, '-', WTC.TASK_CARD, '-', TRIM(WTC.PN), '-', TRIM(WTC.PN_SN)) AS TID, 
        CONCAT(WO.EVENT_ID, '-T00-', WTC.TASK_CARD, '-', TRIM(WTC.PN), '-', TRIM(WTC.PN_SN)) AS UNIQUE_KEY 
        FROM 
        TC_INFO WTC 
        INNER JOIN WO_INFO WO ON WTC.WO = WO.WO 
        WHERE 
        date_format(current_date(), 'yyyy-MM-dd') <= date_format(
            date_sub(coalesce(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE), 35), 
            'yyyy-MM-dd'
        ) 
        OR (
            date_format(current_date(), 'yyyy-MM-dd') > date_format(
            date_sub(coalesce(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE), 35), 
            'yyyy-MM-dd'
            ) 
            AND date_format(current_date(), 'yyyy-MM-dd') <= date_format(
            date_sub(coalesce(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE), 10), 
            'yyyy-MM-dd'
            )
        ) 
        OR (
            date_format(current_date(), 'yyyy-MM-dd') > date_format(
            date_sub(coalesce(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE), 10), 
            'yyyy-MM-dd'
            ) 
            AND date_format(current_date(), 'yyyy-MM-dd') <= date_format(
            coalesce(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE), 
            'yyyy-MM-dd'
            )
        ) 
        ORDER BY 
        UNIQUE_KEY
        """

        logger.info("Started executing TCC query")
        tcc_finalDf = glue_wrapper.execute_spark_sql(tcc_sql_query)
        tcc_finalDf.cache()

        if not tcc_finalDf.isEmpty():
            put_file_to_s3(tcc_finalDf, DISTRIBUTION_FOLDER_PATH_TASK_CARD, FILE_PREFIX_TASK_CARD, None, None)
            logger.info("TCC data written to S3 successfully")
        else:
            logger.info('No TCC records to write')
    except Exception as e:
        logger.error(f"Error in get_tcc_data: {e}")
        raise e

def get_engine_data():
    try:
        logger.info("Started getting engine data")
        
        engine_sql_query = """
        WITH AC_INFO AS (
        SELECT 
            AC.AC, 
            AC_SN AS MSN, 
            AC_TYPE, 
            AC_SERIES, 
            PM.ENGINE, 
            PM.PN_DESCRIPTION, 
            PID.PN, 
            PI.PN AS MASTER_PN, 
            PID.SN, 
            NHA_PN AS TOP_PN, 
            INSTALLED_AC, 
            INSTALLED_POSITION, 
            INSTALLED_DATE,
            NHA_SN AS ALIAS,
            PRORATED_FLAG, 
            GOODS_RCVD_BATCH AS GRB 
        FROM 
            AC_MASTER AC 
            INNER JOIN PN_INVENTORY_DETAIL PID ON AC.AC = PID.INSTALLED_AC 
            INNER JOIN PN_INTERCHANGEABLE PI ON PID.PN = PI.PN_INTERCHANGEABLE 
            INNER JOIN PN_MASTER PM ON PI.PN = PM.PN 
        WHERE 
            (
            (
                PM.ENGINE IN ('ENGINE') 
                AND CHAPTER = '72'
            ) 
            OR (
                PM.ENGINE = 'APU' 
                AND CHAPTER = '49' 
                AND NHA_PN IS NOT NULL
            )
            )
        ), 
        PITA_INFO AS (
        SELECT 
            GOODS_RCVD_BATCH AS GRB, 
            SUM(
            COALESCE(HOURS, 0)
            ) * 60 + SUM(
            COALESCE(MINUTES, 0)
            ) AS ACCRUAL_TOT_M, 
            SUM(
            COALESCE(CYCLES, 0)
            ) AS ACCRUAL_TOT_C, 
            SUM(
            COALESCE(DAYS, 0)
            ) AS ACCRUAL_TOT_D 
        FROM 
            PN_INVENTORY_TIMES_ACCRUAL A 
            INNER JOIN AC_INFO P ON P.GRB = A.GOODS_RCVD_BATCH 
        GROUP BY 
            GOODS_RCVD_BATCH
        ), 
        APTH_INFO AS (
        SELECT 
            GOODS_RCVD_BATCH AS GRB, 
            SUM(
            COALESCE(HOURS_INSTALLED, 0)
            ) * 60 + SUM(
            COALESCE(MINUTES_INSTALLED, 0)
            ) AS RMV_TOT_M, 
            SUM(
            COALESCE(CYCLES_INSTALLED, 0)
            ) AS RMV_TOT_C, 
            SUM(
            COALESCE(DAYS_INSTALLED, 0)
            ) AS RMV_TOT_D 
        FROM 
            AC_PN_TRANSACTION_HISTORY A 
            INNER JOIN AC_INFO P ON P.GRB = A.GOODS_RCVD_BATCH 
        WHERE 
            TRANSACTION_TYPE = 'REMOVE' 
        GROUP BY 
            GOODS_RCVD_BATCH
        ), 
        FLTS_INFO AS (
        SELECT 
            P.PN, 
            P.SN, 
            P.PRORATED_FLAG, 
            CASE WHEN COALESCE(FACTOR_CONTROL, 0) = 0 THEN 1 ELSE FACTOR_CONTROL END AS FACT_CTRL, 
            CASE WHEN COALESCE(PMF.FACTOR_CONTROL_CYCLES, 0) = 0 THEN 1 ELSE PMF.FACTOR_CONTROL_CYCLES END AS FACT_CTRL_CYC, 
            P.INSTALLED_AC, 
            P.INSTALLED_DATE, 
            FLIGHT_HOURS * 60 + FLIGHT_MINUTES AS FLY_M, 
            CYCLES AS FLY_C, 
            OFF_DATETIME 
        FROM 
            AC_ACTUAL_FLIGHTS F 
            INNER JOIN AC_INFO P ON F.AC = P.AC 
            LEFT JOIN PN_MASTER_FACTOR PMF ON P.TOP_PN = PMF.PN 
            AND P.AC_SERIES = PMF.AC_SERIES 
            AND P.AC_TYPE = PMF.AC_TYPE 
        WHERE 
            CYCLES > 0 
            AND OFF_DATETIME >= P.INSTALLED_DATE
        ), 
        TSI_INFO AS (
        SELECT 
            PN, 
            SN, 
            INSTALLED_AC, 
            FACT_CTRL, 
            FACT_CTRL_CYC, 
            SUM(FACT_CTRL * FLY_M) AS TSI_M, 
            SUM(FACT_CTRL_CYC * FLY_C) AS TSI_C 
        FROM 
            FLTS_INFO 
        GROUP BY 
            PN, 
            SN, 
            INSTALLED_AC, 
            FACT_CTRL, 
            FACT_CTRL_CYC
        ) 
        SELECT 
        AC.*, 
        CAST(
            (
            COALESCE(ACCRUAL_TOT_M, 0) + COALESCE(RMV_TOT_M, 0) + COALESCE(TSI_M, 0)
            ) / 60 AS BIGINT
        ) AS HSN, 
        CAST(
            COALESCE(ACCRUAL_TOT_C, 0) + COALESCE(RMV_TOT_C, 0) + COALESCE(TSI_C, 0) AS BIGINT
        ) AS CSN 
        FROM 
        AC_INFO AC 
        LEFT JOIN PITA_INFO PITA ON AC.GRB = PITA.GRB 
        LEFT JOIN APTH_INFO APTH ON AC.GRB = APTH.GRB 
        LEFT JOIN TSI_INFO TSI ON AC.AC = TSI.INSTALLED_AC 
        AND AC.PN = TSI.PN 
        AND AC.SN = TSI.SN 
        ORDER BY 
        AC, 
        ENGINE, 
        INSTALLED_POSITION
        """

        logger.info("Started executing engine query")
        engine_finalDf = glue_wrapper.execute_spark_sql(engine_sql_query)
        engine_finalDf.cache()

        if not engine_finalDf.isEmpty():
            put_file_to_s3(engine_finalDf, DISTRIBUTION_FOLDER_PATH_ENGINE, FILE_PREFIX_ENGINE_DATA, None, None)
            logger.info("Engine data written to S3 successfully")
        else:
            logger.info('No engine records to write')
    except Exception as e:
        logger.error(f"Error in get_engine_data: {e}")
        raise e


if not "pytest" in sys.modules:
    job.init(args["JOB_NAME"], args)

    print(f"Job {JOB_NAME} initialized with args: {args}")



    try:
        jdbc_username, jdbc_password, jdbc_url = get_trax_db_credentials()
        logger.info(f"TRAX DB credentials retrieved successfully")

        df_location_master = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.LOCATION_MASTER",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_wo_vendor_contract = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.WO_VENDOR_CONTRACT",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_vendor_contract = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.VENDOR_CONTRACT",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_relation_master = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.RELATION_MASTER",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_wo_task_card = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.WO_TASK_CARD",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_ac_actual_flights = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.AC_ACTUAL_FLIGHTS",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_ac_master = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.AC_MASTER",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_vendor_contract_task = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.VENDOR_CONTRACT_TASK",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_wo = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.WO",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_pn_inventory_detail = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,  
                "dbtable": "ODB.PN_INVENTORY_DETAIL",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_pn_master = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,  
                "dbtable": "ODB.PN_MASTER",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_task_card_item = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,  
                "dbtable": "ODB.TASK_CARD_ITEM",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_pn_interchangeable = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,  
                "dbtable": "ODB.PN_INTERCHANGEABLE",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_pn_inventory_times_accrual = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,  
                "dbtable": "ODB.PN_INVENTORY_TIMES_ACCRUAL",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_ac_pn_transaction_history = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,  
                "dbtable": "ODB.AC_PN_TRANSACTION_HISTORY",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_pn_master_factor = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,  
                "dbtable": "ODB.PN_MASTER_FACTOR",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

       

        # Create temp views for all tables
        df_wo.createOrReplaceTempView("WO")
        df_location_master.createOrReplaceTempView("LOCATION_MASTER")
        df_wo_vendor_contract.createOrReplaceTempView("WO_VENDOR_CONTRACT")
        df_vendor_contract.createOrReplaceTempView("VENDOR_CONTRACT")
        df_relation_master.createOrReplaceTempView("RELATION_MASTER")
        df_wo_task_card.createOrReplaceTempView("WO_TASK_CARD")
        df_ac_actual_flights.createOrReplaceTempView("AC_ACTUAL_FLIGHTS")
        df_ac_master.createOrReplaceTempView("AC_MASTER")
        df_vendor_contract_task.createOrReplaceTempView("VENDOR_CONTRACT_TASK")
        df_pn_inventory_detail.createOrReplaceTempView("PN_INVENTORY_DETAIL")
        df_pn_master.createOrReplaceTempView("PN_MASTER")
        df_task_card_item.createOrReplaceTempView("TASK_CARD_ITEM")
        df_pn_interchangeable.createOrReplaceTempView("PN_INTERCHANGEABLE")
        df_pn_inventory_times_accrual.createOrReplaceTempView("PN_INVENTORY_TIMES_ACCRUAL")
        df_ac_pn_transaction_history.createOrReplaceTempView("AC_PN_TRANSACTION_HISTORY")
        df_pn_master_factor.createOrReplaceTempView("PN_MASTER_FACTOR")

       

        get_vendor_data(jdbc_url, jdbc_username, jdbc_password)

        yearly_date_pairs = get_yearly_date_pairs(START_DATE, END_DATE)
        for date_pair in yearly_date_pairs:
            get_wo_po_data(date_pair['start_date'], date_pair['end_date'])

        get_tcc_data()

        get_engine_data()

        logger.info(f"Job {JOB_NAME} completed successfully")
      
        


    except Exception as e:
        logger.error(f"Exception Occured in {JOB_NAME} : " + str(e))
        attemptCount = 0
        if 'attempt' in JOB_RUN_ID: 
            attemptCount = JOB_RUN_ID.split('attempt_')[-1]
        
        if attemptCount == '2':
            send_error_to_sns(e)
        raise e
    
    glue_wrapper.commit()

if __name__ == "__main__":
    pass