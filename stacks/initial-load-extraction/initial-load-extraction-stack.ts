import * as cdk from "aws-cdk-lib";
import * as glue from '@aws-cdk/aws-glue-alpha';
import * as iam from "aws-cdk-lib/aws-iam";
import * as s3assets from "aws-cdk-lib/aws-s3-assets";
import * as path from "path";

import { Construct } from "constructs";
import {
  AppConfig,
  DefaultStackSynthesizer,
  InitialLoadExtractionStackConfig,
} from "../../utils/config-util";

export interface InitialLoadExtractionStackProps extends cdk.StackProps {
  config: AppConfig;
  synthesizer: DefaultStackSynthesizer;
  stackConfigProps: InitialLoadExtractionStackConfig;
  errorNotifyTopicArn: string;
}

export class InitialLoadExtractionStack extends cdk.Stack {
  constructor(
    scope: Construct,
    id: string,
    props: InitialLoadExtractionStackProps
  ) {
    super(scope, id, {
      env: {
        account: props.config.account,
        region: props.config.region,
      },
      stackName: `${props.stackConfigProps.stackName}-${props.config.environment}`,
      ...props,
      synthesizer: props.synthesizer,
    });

    const stage = props.config.environment;
    const params = props.stackConfigProps.params;
    const baseName = `${props.stackConfigProps.stackName}-${stage}`;

    const glueJobRole = iam.Role.fromRoleArn(
      this,
      "ConsumptionGlueJobRole",
      `arn:aws:iam::${props.config.account}:role/ac-odh-common-resources-glue-${stage}-consumption-job-role`
    );

    const glueConnection = glue.Connection.fromConnectionName(
      this,
      'GlueConnection',
      params.GLUE_CONNECTION_NAME
    );


    const glueScriptAsset = new s3assets.Asset(this, "GlueScriptAsset", {
      path: path.join(__dirname, "./scripts/data-job.py"),
    });

    new glue.PySparkEtlJob(this, 'InitialLoadExtractionGlueJob', {
      jobName: `${baseName}-data-job`,
      role: glueJobRole,
      description: `Initial load extraction job for ${props.stackConfigProps.stackName}`,
      script: glue.Code.fromBucket(glueScriptAsset.bucket, glueScriptAsset.s3ObjectKey),
      glueVersion: glue.GlueVersion.V4_0,
      workerType: glue.WorkerType.G_1X,
      numberOfWorkers: params.GLUE_JOB_CONFIG_NUMBEROFWORKERS,
      timeout: cdk.Duration.minutes(params.GLUE_JOB_CONFIG_TIMEOUT),
      maxRetries: params.GLUE_JOB_CONFIG_MAXRETRIES,
      connections: [glueConnection],
      maxConcurrentRuns: params.GLUE_JOB_CONFIG_MAXCONCURRENTRUNS,
      defaultArguments: {
        "--TempDir": `s3://${glueScriptAsset.bucket.bucketName}/${baseName}/tmp/`,
        "--enable-continuous-cloudwatch-log":
          params.ENABLE_GLUE_CLOUDWATCH_LOGS,
        "--enable-auto-scaling": "true",
        "--enable-metrics": "true",
        "--enable-observability-metrics": "true",
        "--enable-spark-ui": "true",
        "--spark-event-logs-path": params.GLUE_SPARK_UI_LOGS_PATH,
        "--extra-py-files": params.GLUE_UTILS_PATH,
        "--DISTRIBUTION_BUCKET_NAME": props.config.s3.distributionBucketName,
        "--REGION": props.config.region,
        "--ACCOUNT_ID": props.config.account,
        "--TRAX_DB_SECRET": props.config.secretsManager.traxDbSecret,
        "--START_DATE": params.START_DATE,
        "--END_DATE": params.END_DATE,
        "--DISTRIBUTION_FOLDER_PATH_WO":
          props.config.filePaths.woGeneratingPath,
        "--DISTRIBUTION_FOLDER_PATH_VENDOR":
          props.config.filePaths.vendorGeneratingPath,
        "--DISTRIBUTION_FOLDER_PATH_PO":
          props.config.filePaths.poGeneratingPath,
        "--DISTRIBUTION_FOLDER_PATH_TASK_CARD":
          props.config.filePaths.taskCardGeneratingPath,
        "--DISTRIBUTION_FOLDER_PATH_ENGINE":
          props.config.filePaths.engineDataGeneratingPath,
        "--FILE_PREFIX_WO": props.config.filePaths.filePrefixWO,
        "--FILE_PREFIX_VENDOR": props.config.filePaths.filePrefixVendor,
        "--FILE_PREFIX_PO": props.config.filePaths.filePrefixPO,
        "--FILE_PREFIX_TASK_CARD": props.config.filePaths.filePrefixTaskCard,
        "--FILE_PREFIX_ENGINE_DATA": props.config.filePaths.filePrefixEngines,
        "--SNS_TOPIC_ARN": props.errorNotifyTopicArn,
        "--PENDING_FILE_GENERATING_PATH": props.config.filePaths.pendingPath,
      },
      tags: {
        ...props.config.tags,
        "aws-service": "AWS Glue",
        "unique-id": `${baseName}-data-job`,
      },
    });

    Object.entries(props.config.tags).forEach(([key, value]) => {
      cdk.Tags.of(this).add(key, value);
    });
  }
}
