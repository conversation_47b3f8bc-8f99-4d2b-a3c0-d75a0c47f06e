import { StackProps } from "aws-cdk-lib";
import { Construct } from "constructs";
import { ISecurityGroup, IVpc } from "aws-cdk-lib/aws-ec2";
import * as iam from "aws-cdk-lib/aws-iam";
import * as lambda from "aws-cdk-lib/aws-lambda";
import * as path from "path";
import { SharedLambdaConstruct } from "../../../sharedConstruct";

import {
  AppConfig,
  DeltaLoadExtractionStackConfig,
} from "../../../configs/config.interface";
import { addResourceLevelTags } from "../../../utils/cdk-tagging";

export interface DataExtractionLambdasStackProps extends StackProps {
  config: AppConfig;
  stackConfigProps: DeltaLoadExtractionStackConfig;
  vpc: IVpc;
  securityGroup: ISecurityGroup;
  errorNotifyTopicArn: string;
  lambdaRole: iam.IRole;
}

export class DataExtractionLambdasStack extends Construct {
  public readonly vendorExtractionLambda: lambda.Function;
  public readonly woExtractionLambda: lambda.Function;
  public readonly poExtractionLambda: lambda.Function;
  public readonly taskCardExtractionLambda: lambda.Function;
  public readonly engineDataExtractionLambda: lambda.Function;
  public readonly smcWoDataLambdaConstruct: lambda.Function;

  constructor(
    scope: Construct,
    id: string,
    props: DataExtractionLambdasStackProps
  ) {
    super(scope, id);

    const envName = props.config.environment;
    const baseEnvVars = this.getBaseEnvironmentVariables(props);
    const externalModules = [
      "mysql2",
      "pg",
      "pg-query-stream",
      "sqlite3",
      "better-sqlite3",
      "strong-oracle",
      "oracle",
      "tedious",
      "mysql",
    ];

    const vendorLambdaConstruct = new SharedLambdaConstruct(
      this,
      "VendorExtractionLambda",
      {
        environment: envName,
        vpc: props.vpc,
        securityGroup: props.securityGroup,
        functionName: `${props.stackConfigProps.resources.vendorExtractionLambda.functionName}-${envName}`,
        logging: { retentionDays: props.config.lambda.logging.retentionDays },
        memorySize: props.config.lambda.memorySize,
        timeout: props.config.lambda.timeout,
        handlerFilePath: path.join(
          __dirname,
          "../src/functions/vendor-extraction.ts"
        ),
        handlerFunctionName: "handler",
        description: "Vendor data extraction Lambda for WebFocus Delta Load",
        region: props.config.region,
        envVariables: {
          ...baseEnvVars,
          VENDOR_GENERATING_FILE_PATH:
            props.config.filePaths.vendorGeneratingPath,
          FILE_PREFIX_VENDOR: props.config.filePaths.filePrefixVendor,
        },
        externalModules,
        role: props.lambdaRole,
      }
    );
    this.vendorExtractionLambda = vendorLambdaConstruct.function;

    addResourceLevelTags(
      this.vendorExtractionLambda,
      "lambda",
      `${props.stackConfigProps.resources.vendorExtractionLambda.functionName}-${envName}`
    );

    const woLambdaConstruct = new SharedLambdaConstruct(
      this,
      "WoExtractionLambda",
      {
        environment: envName,
        vpc: props.vpc,
        securityGroup: props.securityGroup,
        functionName: `${props.stackConfigProps.resources.woExtractionLambda.functionName}-${envName}`,
        logging: { retentionDays: props.config.lambda.logging.retentionDays },
        memorySize: props.config.lambda.memorySize,
        timeout: props.config.lambda.timeout,
        handlerFilePath: path.join(
          __dirname,
          "../src/functions/wo-extraction.ts"
        ),
        handlerFunctionName: "handler",
        description:
          "Work Order data extraction Lambda for WebFocus Delta Load",
        region: props.config.region,
        envVariables: {
          ...baseEnvVars,
          WO_GENERATING_FILE_PATH: props.config.filePaths.woGeneratingPath,
          FILE_PREFIX_WO: props.config.filePaths.filePrefixWO,
          FILE_PREFIX_DELETED_WO_DATA:
            props.config.filePaths.filePrefixDeletedWoData,
        },
        externalModules,
        role: props.lambdaRole,
      }
    );
    this.woExtractionLambda = woLambdaConstruct.function;

    addResourceLevelTags(
      this.woExtractionLambda,
      "lambda",
      `${props.stackConfigProps.resources.woExtractionLambda.functionName}-${envName}`
    );

    const poLambdaConstruct = new SharedLambdaConstruct(
      this,
      "PoExtractionLambda",
      {
        environment: envName,
        vpc: props.vpc,
        securityGroup: props.securityGroup,
        functionName: `${props.stackConfigProps.resources.poExtractionLambda.functionName}-${envName}`,
        logging: { retentionDays: props.config.lambda.logging.retentionDays },
        memorySize: props.config.lambda.memorySize,
        timeout: props.config.lambda.timeout,
        handlerFilePath: path.join(
          __dirname,
          "../src/functions/po-extraction.ts"
        ),
        handlerFunctionName: "handler",
        description:
          "Purchase Order data extraction Lambda for WebFocus Delta Load",
        region: props.config.region,
        envVariables: {
          ...baseEnvVars,
          PO_GENERATING_FILE_PATH: props.config.filePaths.poGeneratingPath,
          FILE_PREFIX_PO: props.config.filePaths.filePrefixPO,
        },
        externalModules,
        role: props.lambdaRole,
      }
    );
    this.poExtractionLambda = poLambdaConstruct.function;

    addResourceLevelTags(
      this.poExtractionLambda,
      "lambda",
      `${props.stackConfigProps.resources.poExtractionLambda.functionName}-${envName}`
    );

    const taskCardLambdaConstruct = new SharedLambdaConstruct(
      this,
      "TaskCardExtractionLambda",
      {
        environment: envName,
        vpc: props.vpc,
        securityGroup: props.securityGroup,
        functionName: `${props.stackConfigProps.resources.taskCardExtractionLambda.functionName}-${envName}`,
        logging: { retentionDays: props.config.lambda.logging.retentionDays },
        memorySize: props.config.lambda.memorySize,
        timeout: props.config.lambda.timeout,
        handlerFilePath: path.join(
          __dirname,
          "../src/functions/task-card-extraction.ts"
        ),
        handlerFunctionName: "handler",
        description: "Task Card data extraction Lambda for WebFocus Delta Load",
        region: props.config.region,
        envVariables: {
          ...baseEnvVars,
          TASK_CARD_GENERATING_FILE_PATH:
            props.config.filePaths.taskCardGeneratingPath,
          FILE_PREFIX_TASK_CARD: props.config.filePaths.filePrefixTaskCard,
        },
        externalModules,
        role: props.lambdaRole,
      }
    );
    this.taskCardExtractionLambda = taskCardLambdaConstruct.function;

    addResourceLevelTags(
      this.taskCardExtractionLambda,
      "lambda",
      `${props.stackConfigProps.resources.taskCardExtractionLambda.functionName}-${envName}`
    );

    const engineDataLambdaConstruct = new SharedLambdaConstruct(
      this,
      "EngineDataExtractionLambda",
      {
        environment: envName,
        vpc: props.vpc,
        securityGroup: props.securityGroup,
        functionName: `${props.stackConfigProps.resources.engineDataExtractionLambda.functionName}-${envName}`,
        logging: { retentionDays: props.config.lambda.logging.retentionDays },
        memorySize: props.config.lambda.memorySize,
        timeout: props.config.lambda.timeout,
        handlerFilePath: path.join(
          __dirname,
          "../src/functions/engine-data-extraction.ts"
        ),
        handlerFunctionName: "handler",
        description: "Engine data extraction Lambda for WebFocus Delta Load",
        region: props.config.region,
        envVariables: {
          ...baseEnvVars,
          ENGINE_DATA_GENERATING_FILE_PATH:
            props.config.filePaths.engineDataGeneratingPath,
          FILE_PREFIX_ENGINE_DATA: props.config.filePaths.filePrefixEngines,
        },
        externalModules,
        role: props.lambdaRole,
      }
    );
    this.engineDataExtractionLambda = engineDataLambdaConstruct.function;

    addResourceLevelTags(
      this.engineDataExtractionLambda,
      "lambda",
      `${props.stackConfigProps.resources.engineDataExtractionLambda.functionName}-${envName}`
    );

    const smcWoDataLambdaConstruct = new SharedLambdaConstruct(
      this,
      "SmcWoDataExtractionLambda",
      {
        environment: envName,
        vpc: props.vpc,
        securityGroup: props.securityGroup,
        functionName: `${props.stackConfigProps.resources.smcWoDataExtractionLambda.functionName}-${envName}`,
        logging: { retentionDays: props.config.lambda.logging.retentionDays },
        memorySize: props.config.lambda.memorySize,
        timeout: props.config.lambda.timeout,
        handlerFilePath: path.join(
          __dirname,
          "../src/functions/smc-wo-data-extraction.ts"
        ),
        handlerFunctionName: "handler",
        description: "SMC WO data extraction Lambda for WebFocus Delta Load",
        region: props.config.region,
        envVariables: {
          ...baseEnvVars,
          SMC_WO_DATA_GENERATING_FILE_PATH:
            props.config.filePaths.smcWoDataGeneratingPath,
          FILE_PREFIX_SMC_WO_DATA: props.config.filePaths.filePrefixSmcWoData,
        },
        externalModules,
        role: props.lambdaRole,
      }
    );
    this.smcWoDataLambdaConstruct = smcWoDataLambdaConstruct.function;
    addResourceLevelTags(
      this.smcWoDataLambdaConstruct,
      "lambda",
      `${props.stackConfigProps.resources.smcWoDataExtractionLambda.functionName}-${envName}`
    );
  }

  private getBaseEnvironmentVariables(
    props: DataExtractionLambdasStackProps
  ): Record<string, string> {
    return {
      TRAX_DB_PROXY_SECRET_NAME:
        props.config.secretsManager.traxDbProxySecretName,
      DB_CONNECTION_RETRY_ATTEMPTS:
        props.stackConfigProps.database.connectionRetryAttempts.toString(),
      DB_CONNECTION_MIN_POOL_SIZE:
        props.stackConfigProps.database.minPoolSize.toString(),
      DB_CONNECTION_MAX_POOL_SIZE:
        props.stackConfigProps.database.maxPoolSize.toString(),
      KNEX_DEBUG: props.stackConfigProps.database.knexDebug.toString(),

      DISTRIBUTION_BUCKET_NAME: props.config.s3.distributionBucketName,
      PENDING_FILE_GENERATING_PATH: props.config.filePaths.pendingPath,

      SNS_TOPIC_ARN: props.errorNotifyTopicArn,
    };
  }
}
