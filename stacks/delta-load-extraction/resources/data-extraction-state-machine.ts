import {
  StateMachine,
  TaskInput,
  Succeed,
} from "aws-cdk-lib/aws-stepfunctions";
import { LambdaInvoke, SnsPublish } from "aws-cdk-lib/aws-stepfunctions-tasks";
import { Function } from "aws-cdk-lib/aws-lambda";
import { Rule, Schedule, RuleTargetInput } from "aws-cdk-lib/aws-events";
import { SfnStateMachine } from "aws-cdk-lib/aws-events-targets";
import { Topic } from "aws-cdk-lib/aws-sns";
import { Duration, StackProps } from "aws-cdk-lib";
import { Construct } from "constructs";

import {
  AppConfig,
  DeltaLoadExtractionStackConfig,
} from "../../../configs/config.interface";

export interface DataExtractionStateMachineStackProps extends StackProps {
  config: AppConfig;
  stackConfigProps: DeltaLoadExtractionStackConfig;
  errorNotifyTopicArn: string;
  vendorExtractionLambda: Function;
  woExtractionLambda: Function;
  poExtractionLambda: Function;
  taskCardExtractionLambda: Function;
  engineDataExtractionLambda: Function;
  smcWoDataLambdaConstruct: Function;
}

export class DataExtractionStateMachineStack extends Construct {
  public readonly stateMachine: StateMachine;
  public readonly eventRule: Rule;

  constructor(
    scope: Construct,
    id: string,
    props: DataExtractionStateMachineStackProps
  ) {
    super(scope, id);

    const envName = props.config.environment;

    const retryConfig = {
      errors: [
        "Lambda.ServiceException",
        "Lambda.AWSLambdaException",
        "Lambda.SdkClientException",
        "Lambda.TooManyRequestsException",
      ],
      intervalSeconds: 2,
      maxAttempts: 3,
      backoffRate: 2.0,
    };

    const catchConfig = {
      errors: ["States.ALL"],
      resultPath: "$.error",
    };

    const vendorExtractionTask = new LambdaInvoke(this, "VendorExtraction", {
      lambdaFunction: props.vendorExtractionLambda,
      outputPath: "$.Payload",
    }).addRetry(retryConfig);

    const woExtractionTask = new LambdaInvoke(this, "WoExtraction", {
      lambdaFunction: props.woExtractionLambda,
      outputPath: "$.Payload",
    }).addRetry(retryConfig);

    const poExtractionTask = new LambdaInvoke(this, "PoExtraction", {
      lambdaFunction: props.poExtractionLambda,
      outputPath: "$.Payload",
    }).addRetry(retryConfig);

    const taskCardExtractionTask = new LambdaInvoke(
      this,
      "TaskCardExtraction",
      {
        lambdaFunction: props.taskCardExtractionLambda,
        outputPath: "$.Payload",
      }
    ).addRetry(retryConfig);

    const engineDataExtractionTask = new LambdaInvoke(
      this,
      "EngineDataExtraction",
      {
        lambdaFunction: props.engineDataExtractionLambda,
        outputPath: "$.Payload",
      }
    ).addRetry(retryConfig);

    const smcWoDataExtractionTask = new LambdaInvoke(
      this,
      "SmcWoDataExtraction",
      {
        lambdaFunction: props.smcWoDataLambdaConstruct,
        outputPath: "$.Payload",
      }
    ).addRetry(retryConfig);

    const errorNotifyTopic = Topic.fromTopicArn(
      this,
      "ErrorNotifyTopic",
      props.errorNotifyTopicArn
    );

    const notifyErrorTask = new SnsPublish(this, "NotifyError", {
      topic: errorNotifyTopic,
      subject: "Data Extraction Workflow Failed",
      message: TaskInput.fromJsonPathAt("$.error"),
    });

    const successState = new Succeed(this, "Success", {
      comment: "All extraction processes completed successfully",
    });

    const definition = vendorExtractionTask
      .addCatch(notifyErrorTask, catchConfig)
      .next(
        woExtractionTask
          .addCatch(notifyErrorTask, catchConfig)
          .next(
            poExtractionTask
              .addCatch(notifyErrorTask, catchConfig)
              .next(
                taskCardExtractionTask
                  .addCatch(notifyErrorTask, catchConfig)
                  .next(
                    engineDataExtractionTask
                      .addCatch(notifyErrorTask, catchConfig)
                      .next(
                        smcWoDataExtractionTask
                          .addCatch(notifyErrorTask, catchConfig)
                          .next(successState)
                      )
                  )
              )
          )
      );

    this.stateMachine = new StateMachine(this, "DataExtractionWorkflow", {
      stateMachineName: `${props.stackConfigProps.resources.stateMachine.stateMachineName}-${envName}`,
      definition,
      timeout: Duration.hours(2),
      comment:
        "Sequential data extraction workflow: vendor -> wo -> po -> task-card -> engine-data",
    });

    this.eventRule = new Rule(this, "DataExtractionScheduleRule", {
      ruleName: `${props.stackConfigProps.resources.eventRule.ruleName}-${envName}`,
      description: "Scheduled trigger for data extraction workflow",
      schedule: Schedule.expression(
        props.stackConfigProps.resources.stateMachine.schedule.expression
      ),
      enabled: props.stackConfigProps.resources.stateMachine.schedule.enabled,
    });

    this.eventRule.addTarget(
      new SfnStateMachine(this.stateMachine, {
        input: RuleTargetInput.fromObject({
          source: "eventbridge-schedule",
          timestamp: RuleTargetInput.fromText("$.time").toString(),
        }),
      })
    );
  }
}
