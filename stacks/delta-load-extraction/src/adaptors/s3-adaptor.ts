import { S3Client, PutObjectCommand, PutObjectCommandInput, PutObjectCommandOutput } from "@aws-sdk/client-s3";
import { S3Adaptor } from '../types';

const s3Client = new S3Client({});

const putObject = async (params: PutObjectCommandInput): Promise<PutObjectCommandOutput> => {
  const command = new PutObjectCommand(params);
  return s3Client.send(command);
};

const s3Adaptor: S3Adaptor = {
  putObject,
};

export { putObject };
export default s3Adaptor;
