import { SNSClient, PublishCommand, PublishCommandInput, PublishCommandOutput } from "@aws-sdk/client-sns";
import { SNSAdaptor } from '../types';

const sns = new SNSClient({});

const publishMessage = async (params: PublishCommandInput): Promise<PublishCommandOutput> => {
    const command = new PublishCommand(params);
    return sns.send(command);
};

const snsAdaptor: SNSAdaptor = {
    publishMessage
};

export { publishMessage };
export default snsAdaptor;
