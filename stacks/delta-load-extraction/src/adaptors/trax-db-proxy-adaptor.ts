import { secretsManagerCache } from "ac-utils";
import axios from "axios";
import { DatabaseProxyCredentials } from "../types";
import { LOGGER } from "../utils";

const secrets = new secretsManagerCache();
const secretName = process.env.TRAX_DB_PROXY_SECRET_NAME || "";

const getDbProxyApiKey = async (): Promise<DatabaseProxyCredentials> => {
  try {
    const secret = await secrets.getSecret(secretName);
    const secreteJson: DatabaseProxyCredentials = JSON.parse(secret);

    return secreteJson;
  } catch (error) {
    LOGGER.error("Failed to connect to Oracle database:", error);
    throw error;
  }
};

const executeQuery = async (
  sql: string,
  binds: Record<string, any>
): Promise<any> => {
  try {
    const apiSecrete = await getDbProxyApiKey();
    const headers = {
      "x-api-key": apiSecrete.API_KEY,
      "Content-Type": "application/json",
    };
    const response = await axios.post(
      `${apiSecrete.API_HOST}/execute`,
      {
        sql,
        binds,
      },
      {
        headers: headers,
      }
    );
    console.log("----1------->", response.status);
    console.log("------2----->", response.config);
     console.log("-------3---->", response.request);
    if(response.data.error|| !response.data || !response.data.rows){
      throw new Error(response.data.error);
    }
    LOGGER.info("Trax db proxy call successful",{status: response.status,dataLength: response.data.rows.length});
    return response.data;
  } catch (error) {
    LOGGER.error("Failure while destroying unhealthy connection", error);
    throw error
  }
};

export { executeQuery };
