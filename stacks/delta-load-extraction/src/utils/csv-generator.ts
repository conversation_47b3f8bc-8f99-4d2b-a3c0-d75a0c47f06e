import * as fastcsv from "fast-csv";
import { CSVGenerator } from '../types';

const generateCsv = async (records: any[]): Promise<Buffer> => {
  return new Promise((resolve, reject) => {
    try {
      const csvRows: string[] = [];
      fastcsv
        .write(records, { headers: true })
        .on("data", (row: string) => {
          csvRows.push(row);
        })
        .on("end", () => {
          const csvData = csvRows.join("");
          const buffer = Buffer.from(csvData, "utf8");
          resolve(buffer);
        })
        .on("error", (error: Error) => {
          reject(error);
        });
    } catch (error) {
      reject(error);
    }
  });
};

const csvGenerator: CSVGenerator = {
  generateCSV: generateCsv
};

export { generateCsv };
export default csvGenerator;
