import { DateUtil } from '../types';

const getDateTimeString = (): string => {
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 10).replace(/-/g, "");
    const timeStr = now.toISOString().slice(11, 19).replace(/:/g, "");
    return `${dateStr}_${timeStr}`;
};

const formatDate = (date: Date): string => {
    return date.toISOString().slice(0, 10);
};

const dateUtil: DateUtil = {
    getDateTimeString,
    formatDate
};

export { getDateTimeString, formatDate };
export default dateUtil;
