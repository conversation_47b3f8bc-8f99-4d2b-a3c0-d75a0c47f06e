import * as dbaas from "dbaas-logger";

// Configure dbaas logger
dbaas.dbaasLoggerConfig.streamName = process.env.LOG_STREAM_NAME;
dbaas.dbaasLoggerConfig.debugMode = process.env.DEBUG_MODE;

const LAMBDA_EXIT_STATUS_EXCEPTION = "Exception";
interface LambdaEvent {
  headers?: {
    Authorization?: string;
  };
}

interface Context {
  awsRequestId: string;
}

interface GlobalContext {
  [key: string]: any;
}

const globalContext: GlobalContext = {};

const initialize = (
  event: LambdaEvent,
  context: Context,
  serviceType: string,
  serviceName: string
): void => {
  globalContext["ctx"] = context;
  globalContext["serviceType"] = serviceType;
  globalContext["serviceName"] = serviceName;
  dbaas.start(
    globalContext,
    globalContext.serviceType,
    globalContext.serviceName,
    `${"Logger started"}: ${JSON.stringify(event)}`
  );
  dbaas.logMessage(
    globalContext,
    globalContext.serviceType,
    globalContext.serviceName,
    "Event : ",
    event
  );
  const uniqueReqId = dbaas.logServiceRequest(
    globalContext,
    globalContext.serviceType,
    globalContext.serviceName,
    "Logger started",
    JSON.stringify(event),
    "STRING"
  );
  globalContext["uniqueReqId"] = uniqueReqId;
};

const info = (message: string, event: any = {}): void => {
  dbaas.logMessage(
    globalContext,
    globalContext.serviceType,
    globalContext.serviceName,
    message,
    event
  );
};

const error = (message: string, error: any = {}): void => {
  dbaas.logServiceError(
    globalContext,
    globalContext.serviceType,
    globalContext.serviceName,
    message,
    LAMBDA_EXIT_STATUS_EXCEPTION,
    error,
    globalContext.uniqueReqId
  );
};

const sendLogsToKinesis = (): void => {
  return dbaas.sendLogsToKinesis(globalContext);
};

const logger = {
  initialize,
  info,
  error,
  sendLogsToKinesis,
};

export default logger;
