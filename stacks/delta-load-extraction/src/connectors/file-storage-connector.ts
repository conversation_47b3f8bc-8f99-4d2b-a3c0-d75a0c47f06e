import { s3Adaptor } from "../adaptors";
import { dateUtil } from "../utils";
import { S3UploadParams } from "../types";

const DISTRIBUTION_BUCKET_NAME = process.env.DISTRIBUTION_BUCKET_NAME || "";
const VENDOR_GENERATING_FILE_PATH = process.env.VENDOR_GENERATING_FILE_PATH || "";
const FILE_PREFIX_VENDOR = process.env.FILE_PREFIX_VENDOR || "";
const WO_GENERATING_FILE_PATH = process.env.WO_GENERATING_FILE_PATH || "";
const FILE_PREFIX_WO = process.env.FILE_PREFIX_WO || "";
const PO_GENERATING_FILE_PATH = process.env.PO_GENERATING_FILE_PATH || "";
const FILE_PREFIX_PO = process.env.FILE_PREFIX_PO || "";
const PENDING_FILE_GENERATING_PATH = process.env.PENDING_FILE_GENERATING_PATH || "";
const TASK_CARD_GENERATING_FILE_PATH = process.env.TASK_CARD_GENERATING_FILE_PATH || "";
const FILE_PREFIX_TASK_CARD = process.env.FILE_PREFIX_TASK_CARD || "";
const FILE_PREFIX_ENGINE_DATA = process.env.FILE_PREFIX_ENGINE_DATA || "";
const ENGINE_DATA_GENERATING_FILE_PATH = process.env.ENGINE_DATA_GENERATING_FILE_PATH || "";
const FILE_PREFIX_DELETED_WO_DATA = process.env.FILE_PREFIX_DELETED_WO_DATA || "";
const FILE_PREFIX_SMC_WO_DATA = process.env.FILE_PREFIX_SMC_WO_DATA || "";
const SMC_WO_DATA_GENERATING_FILE_PATH = process.env.SMC_WO_DATA_GENERATING_FILE_PATH || "";

const putVendorFile = async (fileContent: string | Buffer): Promise<S3UploadParams> => {
  const dateTimeString = dateUtil.getDateTimeString();
  const fileName = `${FILE_PREFIX_VENDOR}_${dateTimeString}.csv`;

  const key = VENDOR_GENERATING_FILE_PATH + PENDING_FILE_GENERATING_PATH + fileName;

  const params: S3UploadParams = {
    Bucket: DISTRIBUTION_BUCKET_NAME,
    Key: key,
    Body: fileContent,
    ContentType: "text/csv",
  };
  await s3Adaptor.putObject(params);
  return params;
};

const putWOFile = async (fileContent: string | Buffer): Promise<S3UploadParams> => {
  const dateTimeString = dateUtil.getDateTimeString();
  const fileName = `${FILE_PREFIX_WO}_${dateTimeString}.csv`;

  const key = WO_GENERATING_FILE_PATH + PENDING_FILE_GENERATING_PATH + fileName;

  const params: S3UploadParams = {
    Bucket: DISTRIBUTION_BUCKET_NAME,
    Key: key,
    Body: fileContent,
    ContentType: "text/csv",
  };
  await s3Adaptor.putObject(params);
  return params;
};

const putPOFile = async (fileContent: string | Buffer): Promise<S3UploadParams> => {
  const dateTimeString = dateUtil.getDateTimeString();
  const fileName = `${FILE_PREFIX_PO}_${dateTimeString}.csv`;

  const key = PO_GENERATING_FILE_PATH + PENDING_FILE_GENERATING_PATH + fileName;

  const params: S3UploadParams = {
    Bucket: DISTRIBUTION_BUCKET_NAME,
    Key: key,
    Body: fileContent,
    ContentType: "text/csv",
  };
  await s3Adaptor.putObject(params);
  return params;
};

const putTaskCardFile = async (fileContent: string | Buffer): Promise<S3UploadParams> => {
  const dateTimeString = dateUtil.getDateTimeString();
  const fileName = `${FILE_PREFIX_TASK_CARD}_${dateTimeString}.csv`;

  const key = TASK_CARD_GENERATING_FILE_PATH + PENDING_FILE_GENERATING_PATH + fileName;

  const params: S3UploadParams = {
    Bucket: DISTRIBUTION_BUCKET_NAME,
    Key: key,
    Body: fileContent,
    ContentType: "text/csv",
  };
  await s3Adaptor.putObject(params);
  return params;
};

const putEngineDataFile = async (fileContent: string | Buffer): Promise<S3UploadParams> => {
  const dateTimeString = dateUtil.getDateTimeString();
  const fileName = `${FILE_PREFIX_ENGINE_DATA}_${dateTimeString}.csv`;

  const key = ENGINE_DATA_GENERATING_FILE_PATH + PENDING_FILE_GENERATING_PATH + fileName;

  const params: S3UploadParams = {
    Bucket: DISTRIBUTION_BUCKET_NAME,
    Key: key,
    Body: fileContent,
    ContentType: "text/csv",
  };
  await s3Adaptor.putObject(params);
  return params;
};

const putDeletedWoDataFile = async (fileContent: string | Buffer): Promise<S3UploadParams> => {
  const dateTimeString = dateUtil.getDateTimeString();
  const fileName = `${FILE_PREFIX_DELETED_WO_DATA}_${dateTimeString}.csv`;

  const key = WO_GENERATING_FILE_PATH + PENDING_FILE_GENERATING_PATH + fileName;

  const params: S3UploadParams = {
    Bucket: DISTRIBUTION_BUCKET_NAME,
    Key: key,
    Body: fileContent,
    ContentType: "text/csv",
  };
  await s3Adaptor.putObject(params);
  return params;
};

const putSmcWoDataFile = async (fileContent: string | Buffer): Promise<S3UploadParams> => {
  const dateTimeString = dateUtil.getDateTimeString();
  const fileName = `${FILE_PREFIX_SMC_WO_DATA}_${dateTimeString}.csv`;

  const key = SMC_WO_DATA_GENERATING_FILE_PATH + PENDING_FILE_GENERATING_PATH + fileName;

  const params: S3UploadParams = {
    Bucket: DISTRIBUTION_BUCKET_NAME,
    Key: key,
    Body: fileContent,
    ContentType: "text/csv",
  };
  await s3Adaptor.putObject(params);
  return params;
};

export {
  putVendorFile,
  putWOFile,
  putPOFile,
  putTaskCardFile,
  putEngineDataFile,
  putDeletedWoDataFile,
  putSmcWoDataFile
};
