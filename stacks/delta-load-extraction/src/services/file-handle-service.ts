import { csvGenerator } from "../utils";
import { fileStorageConnector, snsConnector } from "../connectors";
import { S3UploadParams, FileHandleService } from "../types";

const generateCsv = async (records: any[]): Promise<Buffer> => {
  return csvGenerator.generateCSV(records);
};

const vendorFileUpload = async (file: string | Buffer): Promise<S3UploadParams> => {
  return fileStorageConnector.putVendorFile(file);
};

const woFileUpload = async (file: string | Buffer): Promise<S3UploadParams> => {
  return fileStorageConnector.putWOFile(file);
};

const poFileUpload = async (file: string | Buffer): Promise<S3UploadParams> => {
  return fileStorageConnector.putPOFile(file);
};

const taskCardFileUpload = async (file: string | Buffer): Promise<S3UploadParams> => {
  return fileStorageConnector.putTaskCardFile(file);
};

const engineDataFileUpload = async (file: string | Buffer): Promise<S3UploadParams> => {
  return fileStorageConnector.putEngineDataFile(file);
};

const deletedWoDataFileUpload = async (file: string | Buffer): Promise<S3UploadParams> => {
  return fileStorageConnector.putDeletedWoDataFile(file);
};

const smcWoDataFileUpload = async (file: string | Buffer): Promise<S3UploadParams> => {
  return fileStorageConnector.putSmcWoDataFile(file);
};

const notifyFailedEvents = async (subject: string, errorMessage: string): Promise<void> => {
  return snsConnector.publishFailedEvents(subject, errorMessage);
};

const fileHandleService: FileHandleService = {
  generateCsv,
  vendorFileUpload,
  woFileUpload,
  poFileUpload,
  taskCardFileUpload,
  engineDataFileUpload,
  notifyFailedEvents,
  deletedWoDataFileUpload,
  smcWoDataFileUpload
};

export {
  generateCsv,
  vendorFileUpload,
  woFileUpload,
  notifyFailedEvents,
  poFileUpload,
  taskCardFileUpload,
  engineDataFileUpload,
  deletedWoDataFileUpload,
  smcWoDataFileUpload
};

export default fileHandleService;
