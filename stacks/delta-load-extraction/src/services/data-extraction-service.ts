import { db<PERSON>onnector } from "../connectors";
import { LOGGER } from "../utils";
import {
  VendorData,
  WorkOrderData,
  PurchaseOrderData,
  TaskCardData,
  EngineData,
  DataExtractionService,
  DeletedWoData,
  SmcWoData
} from "../types";

const getVendorData = async (): Promise<VendorData[]> => {
  const startTime = Date.now();
  LOGGER.info(`Database connection established in ${Date.now() - startTime}ms`);

  const vendorSql = `SELECT RELATION_CODE,
      NAME,
      MAIL_EMAIL,
      MAIL_PHONE,
      MAIL_ADDRESS_1,
      MAIL_ADDRESS_2,
      MAIL_CITY,
      MAIL_STATE,
      MAIL_COUNTRY,
      MAIL_POST
    FROM ODB.RELATION_MASTER
    WHERE RELATION_TRANSACTION = 'VENDOR'`;
  const vendorData = await dbConnector.executeQuery(vendorSql);

  LOGGER.info(
    `Query completed in ${Date.now() - startTime}ms. Retrieved ${
      vendorData.length
    } vendor records`
  );
  return vendorData as VendorData[];
};

const getWOData = async (
  startDate: Date,
  endDate: Date
): Promise<WorkOrderData[]> => {
  const startTime = Date.now();
  LOGGER.info(`Database connection established in ${Date.now() - startTime}ms`);

  const workorderSql = `
    WITH WO_INIT AS (
        SELECT
        WO.WO,
        WO.PROJECT,
        WO.EXTERNAL_REFERENCE,
        WO.AC,
        WO.WO_CATEGORY,
        WO.PRIORITY,
        WO.STATUS,
        WO.SCHEDULE_ORG_COMPLETION_DATE,
        WO.SCHEDULE_ORG_COMPLETION_HOUR,
        WO.SCHEDULE_ORG_COMPLETION_MINUTE,
        WO.SCHEDULE_COMPLETION_DATE,
        WO.SCHEDULE_COMPLETION_HOUR,
        WO.SCHEDULE_COMPLETION_MINUTE,
        WO.SCHEDULE_START_DATE,
        WO.SCHEDULE_START_HOUR,
        WO.SCHEDULE_START_MINUTE,
        WO.ACTUAL_START_DATE,
        WO.ACTUAL_START_HOUR,
        WO.ACTUAL_START_MINUTE,
        WO.LOCATION,
        WO.VENDOR,
        WO.SITE,
        WO.WO_DESCRIPTION,
        WO.CREATED_DATE,
        WO.CREATED_BY,
        WO.MODIFIED_DATE,
        WO.MODIFIED_BY
        FROM ODB.WO "WO"
        WHERE
        SUBSTR(WO.LOCATION,4,4) IN ('1','2','3','4','5','6','7','8','9')
        AND WO.STATUS IN ( 'CLOSED', 'COMPLETED', 'POSTCOMPLT', 'OPEN' )
        AND (WO.CREATED_BY = 'TRAXIFACE' OR WO.PROJECT = 'EMS')
        AND WO.SCHEDULE_COMPLETION_DATE BETWEEN SYSDATE - 365 AND SYSDATE + 730
    ),
    WO_INFO AS (
        SELECT
        WO.WO,
        WO.AC,
        CASE
            WHEN WO.PROJECT = 'EMS' AND WO.EXTERNAL_REFERENCE IS NULL THEN WO.WO || '_' || WO.AC
            ELSE TRIM(SUBSTR(WO.EXTERNAL_REFERENCE, 1, 10))
        END AS "EVENT_ID",
        CASE
            WHEN WO.PROJECT = 'EMS' THEN 'EMS'
            ELSE (
            CASE
                WHEN WO.WO_CATEGORY = 'HMV' AND WO.PRIORITY = 'LOW' THEN 'HML'
                ELSE (
                CASE
                    WHEN WO.WO_CATEGORY IN ('OOS', 'PRK') THEN WO.WO_CATEGORY
                END
                )
            END
            )
        END AS "EVENT_TYPE",
        CASE
            WHEN WO.STATUS IN ('OPEN', 'COMPLETED')
            THEN TO_CHAR(WO.SCHEDULE_ORG_COMPLETION_DATE, 'YYYY-MM-DD') || ' ' ||
                TRIM(SUBSTR(LPAD(WO.SCHEDULE_ORG_COMPLETION_HOUR, 2, '0') || ':' ||
                            LPAD(WO.SCHEDULE_ORG_COMPLETION_MINUTE, 2, '0'), 1, 8))
            ELSE TO_CHAR(WO.SCHEDULE_COMPLETION_DATE, 'YYYY-MM-DD') || ' ' ||
                TRIM(SUBSTR(LPAD(WO.SCHEDULE_COMPLETION_HOUR, 2, '0') || ':' ||
                            LPAD(WO.SCHEDULE_COMPLETION_MINUTE, 2, '0'), 1, 8))
        END AS "ACTUAL_COMPLETION_DATETIME",
        CASE 
            WHEN TO_CHAR(SYSDATE, 'YYYY-MM-DD') < TO_CHAR(WO.ACTUAL_START_DATE - 35, 'YYYY-MM-DD') 
                AND WO.STATUS = 'OPEN' THEN 5
            WHEN TO_CHAR(SYSDATE, 'YYYY-MM-DD') >= TO_CHAR(WO.ACTUAL_START_DATE - 35, 'YYYY-MM-DD') 
                AND TO_CHAR(SYSDATE, 'YYYY-MM-DD') < TO_CHAR(WO.ACTUAL_START_DATE - 10, 'YYYY-MM-DD') 
                AND WO.STATUS = 'OPEN' THEN 4
            WHEN TO_CHAR(SYSDATE, 'YYYY-MM-DD') >= TO_CHAR(WO.ACTUAL_START_DATE - 10, 'YYYY-MM-DD') 
                AND TO_CHAR(SYSDATE, 'YYYY-MM-DD') < TO_CHAR(WO.ACTUAL_START_DATE, 'YYYY-MM-DD') 
                AND WO.STATUS = 'OPEN' THEN 3
            WHEN TO_CHAR(SYSDATE, 'YYYY-MM-DD') >= TO_CHAR(WO.ACTUAL_START_DATE, 'YYYY-MM-DD') 
                AND WO.STATUS = 'OPEN' THEN 2
            WHEN WO.STATUS IN ('POSTCOMPLT', 'COMPLETED', 'CLOSED') THEN 1
        END AS "CHECK_STATUS",
        WO.SCHEDULE_START_DATE,
        TO_CHAR(WO.ACTUAL_START_DATE, 'YYYY-MM-DD') || ' ' ||
        TRIM(SUBSTR(LPAD(WO.ACTUAL_START_HOUR, 2, '0') || ':' ||
                    LPAD(WO.ACTUAL_START_MINUTE, 2, '0'), 1, 8)) AS "ACTUAL_START_DATETIME",
        WO.LOCATION,
        CASE	WHEN INSTR ( WO.WO_DESCRIPTION,',' ) <>0
                THEN SUBSTR ( SUBSTR ( WO.WO_DESCRIPTION,( INSTR ( WO.WO_DESCRIPTION,',' ) +0 ),( INSTR ( WO.WO_DESCRIPTION,',',1,2 ) -0 ) - ( INSTR ( WO.WO_DESCRIPTION,',' ) ) ) ,2,30 )
                ELSE SUBSTR ( WO.WO_DESCRIPTION,1,55 )
        END AS "DESCRIPTION",
        WO.VENDOR,
        WO.SCHEDULE_COMPLETION_DATE,
        WO.SCHEDULE_COMPLETION_HOUR,
        WO.SCHEDULE_COMPLETION_MINUTE,
        WO.SCHEDULE_ORG_COMPLETION_DATE,
        WO.SCHEDULE_ORG_COMPLETION_HOUR,
        WO.SCHEDULE_ORG_COMPLETION_MINUTE,
        WO.SCHEDULE_START_HOUR,
        WO.SCHEDULE_START_MINUTE,
        WO.SITE,
        WO.WO_DESCRIPTION,
        WO.PROJECT,
        WO.WO_CATEGORY,
        WO.PRIORITY,
        WO.EXTERNAL_REFERENCE,
        WO.STATUS,
        WO.CREATED_DATE,
        WO.CREATED_BY,
        WO.MODIFIED_DATE,
        WO.MODIFIED_BY
        FROM WO_INIT "WO"
    ),
    TASK_COUNTS AS (
        SELECT
        WOTC.WO,
        COUNT(CASE WHEN NVL(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD END) AS "TOTAL_TC",
        COUNT(CASE WHEN WOTC.STATUS = 'CANCEL' AND NVL(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD END) AS "TOTAL_CANCEL_TC",
        COUNT(CASE WHEN WOTC.TASK_CARD NOT LIKE 'NR-%' AND NVL(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD END) AS "TOTAL_ROUTINE_TC",
        COUNT(CASE WHEN WOTC.TASK_CARD NOT LIKE 'NR-%' AND WOTC.STATUS = 'OPEN' AND NVL(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD END) AS "OPEN_ROUTINE_TC",
        COUNT(CASE WHEN WOTC.TASK_CARD NOT LIKE 'NR-%' AND WOTC.STATUS = 'CLOSED' AND NVL(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD END) AS "CLOSED_ROUTINE_TC",
        COUNT(CASE WHEN WOTC.TASK_CARD NOT LIKE 'NR-%' AND WOTC.STATUS = 'CANCEL' AND NVL(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD END) AS "CANCEL_ROUTINE_TC",
        COUNT(CASE WHEN WOTC.TASK_CARD LIKE 'NR-%' AND NVL(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD END) AS "TOTAL_NON_ROUTINE_TC",
        COUNT(CASE WHEN WOTC.TASK_CARD LIKE 'NR-%' AND WOTC.STATUS = 'OPEN' AND NVL(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD END) AS "OPEN_NON_ROUTINE_TC",
        COUNT(CASE WHEN WOTC.TASK_CARD LIKE 'NR-%' AND WOTC.STATUS = 'CLOSED' AND NVL(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD END) AS "CLOSED_NON_ROUTINE_TC",
        COUNT(CASE WHEN WOTC.TASK_CARD LIKE 'NR-%' AND WOTC.STATUS = 'CANCEL' AND NVL(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD END) AS "CANCEL_NON_ROUTINE_TC",
        SUM(CASE WHEN WOTC.STATUS IN ('CLOSED', 'CANCEL') AND NVL(WOTC.TYPE, 'N/A') <> 'CA' THEN 1 ELSE 0 END) AS "COMPLETED_OR_CANCELED_TC"
        FROM ODB.WO_TASK_CARD WOTC
        WHERE WOTC.WO IN (SELECT WO FROM WO_INFO)
        GROUP BY WOTC.WO
    ),
    MAN_HOURS AS (
        SELECT
        WOTC.WO,
        NVL(SUM(CASE WHEN VCT.MAN_HOURS <> 0 THEN VCT.MAN_HOURS END), 0) AS "TOTAL_MH",
        NVL(SUM(CASE WHEN WOTC.STATUS <> 'OPEN' AND VCT.MAN_HOURS <> 0 THEN VCT.MAN_HOURS END), 0) AS "TOTAL_MH_COMPLETED"
        FROM ODB.WO_TASK_CARD WOTC
        INNER JOIN ODB.VENDOR_CONTRACT_TASK VCT ON WOTC.TASK_CARD = VCT.TASK_CARD
        WHERE WOTC.WO IN (SELECT WO FROM WO_INFO)
        GROUP BY WOTC.WO
    ),
    LATEST_FLIGHT AS (
        SELECT
        AF.AC,
        AF.FLIGHT,
        AF.ORIGIN,
        AF.DESTINATION,
        AF.FLIGHT_DATE,
        AF.ON_HOUR,
        AF.ON_MINUTE
        FROM ODB.AC_ACTUAL_FLIGHTS AF
        WHERE (AF.AC, AF.CREATED_DATE) IN (
        SELECT AC, MAX(CREATED_DATE)
        FROM ODB.AC_ACTUAL_FLIGHTS
        WHERE AC IN (SELECT AC FROM WO_INFO)
        GROUP BY AC
        )
    )
    SELECT
        WO.WO,
        WO.EVENT_ID,
        WO.EVENT_TYPE,
        CASE
          WHEN TRIM(WO.WO_DESCRIPTION) LIKE '%AOG%'  THEN 'AOG'
          WHEN TRIM(WO.WO_DESCRIPTION) LIKE 'EXIT%'  THEN 'EXIT'
          WHEN TRIM(WO.WO_DESCRIPTION) LIKE 'STC%'   THEN 'STC'
          WHEN TRIM(WO.WO_DESCRIPTION) LIKE '%RTS%'  THEN 'RTS'
          ELSE WO.EVENT_TYPE
        END AS EVENT_REPORT_TYPE,
        WO.CHECK_STATUS,
        TO_DATE(WO.ACTUAL_COMPLETION_DATETIME, 'YYYY-MM-DD HH24:MI:SS') - 
        TO_DATE(WO.ACTUAL_START_DATETIME, 'YYYY-MM-DD HH24:MI:SS') AS "DURATION",
        WO.ACTUAL_START_DATETIME,
        CASE
        WHEN TC.TOTAL_TC = 0 OR TC.COMPLETED_OR_CANCELED_TC = 0 THEN 0
        WHEN WO.SCHEDULE_START_DATE > SYSDATE THEN 0
        ELSE ROUND(TC.COMPLETED_OR_CANCELED_TC / TC.TOTAL_TC * 100)
        END AS "PERCENT_COMPLETED",
        TC.TOTAL_TC,
        TC.TOTAL_CANCEL_TC,
        TC.TOTAL_ROUTINE_TC,
        TC.OPEN_ROUTINE_TC,
        TC.CLOSED_ROUTINE_TC,
        TC.CANCEL_ROUTINE_TC,
        TC.TOTAL_NON_ROUTINE_TC,
        TC.OPEN_NON_ROUTINE_TC,
        TC.CLOSED_NON_ROUTINE_TC,
        TC.CANCEL_NON_ROUTINE_TC,
        MH.TOTAL_MH,
        MH.TOTAL_MH_COMPLETED,
        CASE
        WHEN MH.TOTAL_MH = 0 THEN 0
        ELSE ROUND(MH.TOTAL_MH_COMPLETED / MH.TOTAL_MH * 100)
        END AS "MH_ROUTINE_CHECK_COMPLETION",
        AC.AC_TYPE AS "TYPE",
        AC.AC_SN,
        AC.AC_FLIGHT_HOURS,
        AC.AC_FLIGHT_MINUTES,
        AC.AC_CYCLES,
        AC.LAST_AC_REGISTRATION,
        SUBSTR(AC.BASIC_NUMBER, 1, 2) AS "OPERATOR",
        LF.AC,
        LF.FLIGHT,
        LF.ORIGIN,
        LF.DESTINATION,
        LF.FLIGHT_DATE,
        LF.ON_HOUR,
        LF.ON_MINUTE,
        WO.LOCATION,
        WO.DESCRIPTION,
        WO.VENDOR,
        WO.SCHEDULE_START_DATE,
        WO.SCHEDULE_START_HOUR,
        WO.SCHEDULE_START_MINUTE,
        WO.SCHEDULE_COMPLETION_DATE,
        WO.SCHEDULE_COMPLETION_HOUR,
        WO.SCHEDULE_COMPLETION_MINUTE,
        WO.SCHEDULE_ORG_COMPLETION_DATE,
        WO.SCHEDULE_ORG_COMPLETION_HOUR,
        WO.SCHEDULE_ORG_COMPLETION_MINUTE,
        WO.SITE,
        WO.PROJECT,
        WO.WO_CATEGORY,
        WO.PRIORITY,
        WO.EXTERNAL_REFERENCE,
        WO.STATUS,
        WO.CREATED_DATE,
        WO.CREATED_BY,
        WO.MODIFIED_DATE,
        WO.MODIFIED_BY,
        LM.TIME_ZONE_NAME
    FROM WO_INFO WO
    LEFT JOIN TASK_COUNTS TC ON WO.WO = TC.WO
    LEFT JOIN MAN_HOURS MH ON WO.WO = MH.WO
    LEFT JOIN ODB.AC_MASTER AC ON WO.AC = AC.AC
    LEFT JOIN LATEST_FLIGHT LF ON WO.AC = LF.AC
    LEFT JOIN ODB.LOCATION_MASTER LM ON WO.LOCATION = LM.LOCATION
    ORDER BY WO.SCHEDULE_START_DATE
  `;
  const workorderDataRows = await dbConnector.executeQuery(workorderSql);

  const formatDateColumns = (data) => {
    const dateColumns = [
      "SCHEDULE_START_DATE",
      "SCHEDULE_COMPLETION_DATE",
      "SCHEDULE_ORG_COMPLETION_DATE",
      "ACTUAL_START_DATE",
      "CREATED_DATE",
      "MODIFIED_DATE",
      "FLIGHT_DATE",
    ];

    return data.map((row) => {
      const formattedRow = { ...row };
      dateColumns.forEach((column) => {
        if (formattedRow[column] && formattedRow[column] instanceof Date) {
          formattedRow[column] = formattedRow[column].toISOString();
        }
      });
      return formattedRow;
    });
  };

  const formattedData = formatDateColumns(workorderDataRows);

  LOGGER.info(
    `Query completed in ${Date.now() - startTime}ms. Retrieved ${
      workorderDataRows.length
    } work order records`
  );
  return formattedData as WorkOrderData[];
};

const getPoData = async (
  startDate: Date,
  endDate: Date
): Promise<PurchaseOrderData[]> => {
  const startTime = Date.now();

  LOGGER.info(`Database connection established in ${Date.now() - startTime}ms`);
  const poDataSql = `SELECT WO.WO,
      WO.EXTERNAL_REFERENCE,
      VC.CURRENCY as CURRENCY_CODE,
      RV.RELATION_CODE as SUPPLIER_CODE,
      RV.MAIL_ADDRESS_1 as SUPPLIER_ADDRESS1,
      RV.MAIL_ADDRESS_2 as SUPPLIER_ADDRESS2,
      RV.MAIL_CITY as SUPPLIER_CITY,
      RV.MAIL_POST as SUPPLIER_POST,
      RV.MAIL_STATE as SUPPLIER_STATE,
      RV.MAIL_COUNTRY as SUPPLIER_COUNTRY,
      RV.MAIL_PHONE as SUPPLIER_PHONE,
      RV.MAIL_FAX as SUPPLIER_FAX,
      RV.MAIL_CELL as SUPPLIER_CELL,
      RV.MAIL_EMAIL as SUPPLIER_EMAIL,
      SUBSTR(NVL(RV.NAME,' - NO SUPPLIER - '),1,40) as SUPPLIER
FROM ODB.WO WO
LEFT JOIN ODB.WO_VENDOR_CONTRACT WVC ON WO.WO = WVC.WO
LEFT JOIN ODB.VENDOR_CONTRACT VC ON WO.VENDOR = VC.VENDOR 
    AND WO.LOCATION = VC.LOCATION 
    AND WVC.CONTRACT_TYPE = VC.CONTRACT_TYPE
LEFT JOIN ODB.RELATION_MASTER RV ON WO.VENDOR = RV.RELATION_CODE 
    AND NVL(RV.RELATION_TRANSACTION, 'VENDOR') = 'VENDOR'
WHERE SUBSTR(WO.LOCATION,4,4) IN ('1','2','3','4','5','6','7','8','9')
    AND WO.STATUS IN ('COMPLETED', 'POSTCOMPLT', 'OPEN', 'CLOSED')
    AND (WO.CREATED_BY = 'TRAXIFACE' OR WO.PROJECT = 'EMS')
    AND WO.SCHEDULE_COMPLETION_DATE BETWEEN SYSDATE - 365 AND SYSDATE + 730
      `;
  const poData = await dbConnector.executeQuery(poDataSql);

  LOGGER.info(
    `Query completed in ${Date.now() - startTime}ms. Retrieved ${
      poData.length
    } purchase order records`
  );
  return poData as PurchaseOrderData[];
};

const getTaskCardData = async (): Promise<TaskCardData[]> => {
  const startTime = Date.now();

  LOGGER.info(`Database connection established in ${Date.now() - startTime}ms`);

  const taskCardDataSql = `
    WITH WO_INFO AS (
        SELECT
            WO.WO,
            CASE 
                WHEN PROJECT = 'EMS' THEN WO.WO || '_' || WO.AC 
                ELSE TRIM(SUBSTR(WO.EXTERNAL_REFERENCE, 1, 10))
            END AS EVENT_ID,
            WO.SCHEDULE_START_DATE,
            WO.SCHEDULE_START_DATE AS ACTUAL_START_DATE,
            WO.SCHEDULE_COMPLETION_DATE
        FROM ODB.WO
        WHERE SUBSTR(WO.LOCATION, 4, 4) IN ('1', '2', '3', '4', '5', '6', '7', '8', '9')
            AND (WO.CREATED_BY = 'TRAXIFACE' OR WO.PROJECT = 'EMS')
            AND WO.STATUS IN ('POSTCOMPLT', 'OPEN')
            AND WO.SCHEDULE_COMPLETION_DATE BETWEEN SYSDATE - 365 AND SYSDATE + 730
    ),
    TC_INFO AS (
        SELECT 
            WOTC.WO,
            WOTC.TASK_CARD,
            CASE 
                WHEN PID.INSTALLED_POSITION = 'ONLY' THEN SUBSTR(PN.PN_DESCRIPTION, 1, 3) 
                ELSE PID.INSTALLED_POSITION 
            END AS POSITION,
            WOTC.TASK_CARD_DESCRIPTION,
            WOTC.STATUS,
            WOTC.TASK_CARD_NUMBERING_SYSTEM,
            WOTC.REVISION,
            WOTC.PN,
            WOTC.PN_SN,
            TCI.SKILL
        FROM WO_INFO WO
            LEFT JOIN ODB.WO_TASK_CARD WOTC ON WO.WO = WOTC.WO
            LEFT JOIN ODB.PN_INVENTORY_DETAIL PID ON WOTC.PN = PID.PN AND WOTC.PN_SN = PID.SN
            LEFT JOIN ODB.PN_MASTER PN ON PID.PN = PN.PN,
            ODB.TASK_CARD_ITEM TCI
        WHERE WOTC.TASK_CARD = TCI.TASK_CARD
            AND TCI.TASK_CARD_ITEM = 1

        UNION

        SELECT 
            WOTC.WO,
            WOTC.TASK_CARD,
            NULL AS POSITION,
            WOTC.TASK_CARD_DESCRIPTION,
            WOTC.STATUS,
            WOTC.TASK_CARD_NUMBERING_SYSTEM,
            WOTC.REVISION,
            WOTC.PN,
            WOTC.PN_SN,
            NULL AS SKILL
        FROM WO_INFO WO
            LEFT JOIN ODB.WO_TASK_CARD WOTC ON WO.WO = WOTC.WO
        WHERE WOTC.TASK_CARD LIKE 'NR%'
        ORDER BY 6
    )
    SELECT 
        WO.WO,
        WO.EVENT_ID,
        'T35' AS PERIOD,
        WTC.TASK_CARD,
        TRIM(WTC.PN) AS PN,
        TRIM(WTC.PN_SN) AS PN_SN,
        WO.EVENT_ID || '-T35' AS PID,
        WO.EVENT_ID || '-' || WTC.TASK_CARD || '-' || TRIM(WTC.PN) || '-' || TRIM(WTC.PN_SN) AS TID,
        WO.EVENT_ID || '-T35-' || WTC.TASK_CARD || '-' || TRIM(WTC.PN) || '-' || TRIM(WTC.PN_SN) AS UNIQUE_KEY
    FROM TC_INFO WTC
        INNER JOIN WO_INFO WO ON WTC.WO = WO.WO
    WHERE TO_CHAR(SYSDATE, 'YYYY-MM-DD') <= TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 35, 'YYYY-MM-DD')

    UNION ALL

    SELECT 
        WO.WO,
        WO.EVENT_ID,
        'T10' AS PERIOD,
        WTC.TASK_CARD,
        TRIM(WTC.PN) AS PN,
        TRIM(WTC.PN_SN) AS PN_SN,
        WO.EVENT_ID || '-T10' AS PID,
        WO.EVENT_ID || '-' || WTC.TASK_CARD || '-' || TRIM(WTC.PN) || '-' || TRIM(WTC.PN_SN) AS TID,
        WO.EVENT_ID || '-T10-' || WTC.TASK_CARD || '-' || TRIM(WTC.PN) || '-' || TRIM(WTC.PN_SN) AS UNIQUE_KEY
    FROM TC_INFO WTC
        INNER JOIN WO_INFO WO ON WTC.WO = WO.WO
    WHERE TO_CHAR(SYSDATE, 'YYYY-MM-DD') <= TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 35, 'YYYY-MM-DD')
        OR (TO_CHAR(SYSDATE, 'YYYY-MM-DD') > TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 35, 'YYYY-MM-DD') 
            AND TO_CHAR(SYSDATE, 'YYYY-MM-DD') <= TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 10, 'YYYY-MM-DD'))

    UNION ALL

    SELECT 
        WO.WO,
        WO.EVENT_ID,
        'T00' AS PERIOD,
        WTC.TASK_CARD,
        TRIM(WTC.PN) AS PN,
        TRIM(WTC.PN_SN) AS PN_SN,
        WO.EVENT_ID || '-T00' AS PID,
        WO.EVENT_ID || '-' || WTC.TASK_CARD || '-' || TRIM(WTC.PN) || '-' || TRIM(WTC.PN_SN) AS TID,
        WO.EVENT_ID || '-T00-' || WTC.TASK_CARD || '-' || TRIM(WTC.PN) || '-' || TRIM(WTC.PN_SN) AS UNIQUE_KEY
    FROM TC_INFO WTC
        INNER JOIN WO_INFO WO ON WTC.WO = WO.WO
    WHERE TO_CHAR(SYSDATE, 'YYYY-MM-DD') <= TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 35, 'YYYY-MM-DD')
        OR (TO_CHAR(SYSDATE, 'YYYY-MM-DD') > TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 35, 'YYYY-MM-DD') 
            AND TO_CHAR(SYSDATE, 'YYYY-MM-DD') <= TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 10, 'YYYY-MM-DD'))
        OR (TO_CHAR(SYSDATE, 'YYYY-MM-DD') > TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 10, 'YYYY-MM-DD') 
            AND TO_CHAR(SYSDATE, 'YYYY-MM-DD') <= TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE), 'YYYY-MM-DD'))
    ORDER BY UNIQUE_KEY
  `;
  const taskCardData = await dbConnector.executeQuery(taskCardDataSql);

  LOGGER.info(
    `Query completed in ${Date.now() - startTime}ms. Retrieved ${
      taskCardData.length
    } task card records`
  );
  return taskCardData as TaskCardData[];
};

const getEnginesData = async (): Promise<EngineData[]> => {
  const startTime = Date.now();
  LOGGER.info(`Database connection established in ${Date.now() - startTime}ms`);

  const engineDataSql = `
    WITH
        AC_INFO AS (
            SELECT
                AC.AC,
                AC_SN AS MSN,
                AC_TYPE,
                AC_SERIES,
                PM.ENGINE,
                PM.PN_DESCRIPTION,
                PID.PN,
                PI.PN AS MASTER_PN,
                PID.SN,
                NHA_PN AS TOP_PN,
                INSTALLED_AC,
                INSTALLED_POSITION,
                INSTALLED_DATE,
                NHA_SN AS ALIAS,
                PRORATED_FLAG,
                GOODS_RCVD_BATCH AS GRB
            FROM
                ODB.AC_MASTER AC
                INNER JOIN ODB.PN_INVENTORY_DETAIL PID ON AC.AC = PID.INSTALLED_AC
                INNER JOIN ODB.PN_INTERCHANGEABLE PI ON PID.PN = PI.PN_INTERCHANGEABLE
                INNER JOIN ODB.PN_MASTER PM ON PI.PN = PM.PN
            WHERE
                (
                    (
                        PM.ENGINE IN ('ENGINE')
                        AND CHAPTER = '72'
                    )
                    OR (
                        PM.ENGINE = 'APU'
                        AND CHAPTER = '49'
                        AND NHA_PN IS NOT NULL
                    )
                )
        ),
        PITA_INFO AS (
            SELECT
                GOODS_RCVD_BATCH AS GRB,
                SUM(NVL (HOURS, 0)) * 60 + SUM(NVL (MINUTES, 0)) AS ACCRUAL_TOT_M,
                SUM(NVL (CYCLES, 0)) AS ACCRUAL_TOT_C,
                SUM(NVL (DAYS, 0)) AS ACCRUAL_TOT_D
            FROM
                ODB.PN_INVENTORY_TIMES_ACCRUAL A
                INNER JOIN AC_INFO P ON P.GRB = A.GOODS_RCVD_BATCH
            GROUP BY
                GOODS_RCVD_BATCH
        ),
        APTH_INFO AS (
            SELECT
                GOODS_RCVD_BATCH AS GRB,
                SUM(NVL (HOURS_INSTALLED, 0)) * 60 + SUM(NVL (MINUTES_INSTALLED, 0)) AS RMV_TOT_M,
                SUM(NVL (CYCLES_INSTALLED, 0)) AS RMV_TOT_C,
                SUM(NVL (DAYS_INSTALLED, 0)) AS RMV_TOT_D
            FROM
                ODB.AC_PN_TRANSACTION_HISTORY A
                INNER JOIN AC_INFO P ON P.GRB = A.GOODS_RCVD_BATCH
            WHERE
                TRANSACTION_TYPE = 'REMOVE'
            GROUP BY
                GOODS_RCVD_BATCH
        ),
        FLTS_INFO AS (
            SELECT
                P.PN,
                P.SN,
                P.PRORATED_FLAG,
                CASE NVL (FACTOR_CONTROL, 0)
                    WHEN 0 THEN 1
                    ELSE FACTOR_CONTROL
                END AS FACT_CTRL,
                CASE NVL (PMF.FACTOR_CONTROL_CYCLES, 0)
                    WHEN 0 THEN 1
                    ELSE PMF.FACTOR_CONTROL_CYCLES
                END AS FACT_CTRL_CYC,
                P.INSTALLED_AC,
                P.INSTALLED_DATE,
                FLIGHT_HOURS * 60 + FLIGHT_MINUTES AS FLY_M,
                CYCLES AS FLY_C,
                OFF_DATETIME
            FROM
                ODB.AC_ACTUAL_FLIGHTS F
                INNER JOIN AC_INFO P ON F.AC = P.AC
                LEFT JOIN ODB.PN_MASTER_FACTOR PMF ON P.TOP_PN = PMF.PN
                AND P.AC_SERIES = PMF.AC_SERIES
                AND P.AC_TYPE = PMF.AC_TYPE
            WHERE
                CYCLES > 0
                AND OFF_DATETIME >= P.INSTALLED_DATE
        ),
        TSI_INFO AS (
            SELECT
                PN,
                SN,
                INSTALLED_AC,
                FACT_CTRL,
                FACT_CTRL_CYC,
                SUM(FACT_CTRL * FLY_M) AS TSI_M,
                SUM(FACT_CTRL_CYC * FLY_C) AS TSI_C
            FROM
                FLTS_INFO
            GROUP BY
                PN,
                SN,
                INSTALLED_AC,
                FACT_CTRL,
                FACT_CTRL_CYC
        )
    SELECT
        AC.*,
        TRUNC (
            (
                NVL (ACCRUAL_TOT_M, 0) + NVL (RMV_TOT_M, 0) + NVL (TSI_M, 0)
            ) / 60
        ) AS HSN,
        TRUNC (
            NVL (ACCRUAL_TOT_C, 0) + NVL (RMV_TOT_C, 0) + NVL (TSI_C, 0)
        ) AS CSN
    FROM
        AC_INFO AC
        LEFT JOIN PITA_INFO PITA ON AC.GRB = PITA.GRB
        LEFT JOIN APTH_INFO APTH ON AC.GRB = APTH.GRB
        LEFT JOIN TSI_INFO TSI ON AC.AC = TSI.INSTALLED_AC
        AND AC.PN = TSI.PN
        AND AC.SN = TSI.SN
    ORDER BY
        AC,
        ENGINE,
        INSTALLED_POSITION
  `;
  const engineData = await dbConnector.executeQuery(engineDataSql);

  LOGGER.info(
    `Query completed in ${Date.now() - startTime}ms. Retrieved ${
      engineData.length
    } engine data records`
  );
  return engineData as EngineData[];
};

const getDeletedWoData = async (): Promise<DeletedWoData[]> => {
  const startTime = Date.now();
  LOGGER.info(`Database connection established in ${Date.now() - startTime}ms`);

  const deletedWoDataSql = `
    SELECT
	    WO
        ,CASE WHEN PROJECT = 'EMS' THEN WO || '_' || AC ELSE TRIM ( SUBSTR ( EXTERNAL_REFERENCE,1,10 ) ) END AS "EVENT_ID"
    FROM ODB.WO_AUDIT
    WHERE TRANSACTION_TYPE = 'DELETE' AND WO NOT IN ('1229259','1229255')
  `;
  const deletedWoData = await dbConnector.executeQuery(deletedWoDataSql);

  LOGGER.info(
    `Query completed in ${Date.now() - startTime}ms. Retrieved ${
      deletedWoData.length
    } engine data records`
  );
  return deletedWoData as DeletedWoData[];
};

const getSmcWoData = async (): Promise<SmcWoData[]> => {
  const startTime = Date.now();
  LOGGER.info(`Database connection established in ${Date.now() - startTime}ms`);

  const smcWoDataSql = `
    WITH PARAMS AS
(
	SELECT
	  TO_DATE(TO_CHAR(SYSDATE, 'YYYY-MM-DD') || ' 00:00', 'YYYY-MM-DD HH24:MI') AS "DATE1"
	    ,TO_DATE(TO_CHAR(SYSDATE + 11, 'YYYY-MM-DD') || ' 23:59', 'YYYY-MM-DD HH24:MI') AS "DATE2"

	FROM DUAL
), STATION_INFO AS (
    SELECT
        NVL(STATION_CODE, LOCATION) AS STATION,
        LOCATION
    FROM ODB.LOCATION_MASTER
    WHERE INVENTORY_QUARANTINE = 'N'
),
TASK_CARD_PN_INFO AS (
    -- ************** ROUTINE TASK_CARD
    SELECT
        WO.WO,
        WO.AC,
        NVL(LM.STATION, WO.LOCATION) AS WO_STATION,
        TO_CHAR(TRUNC(WO.SCHEDULE_START_DATE) + WO.SCHEDULE_START_HOUR/24 + WO.SCHEDULE_START_MINUTE/24/60,'YYYY-MM-DD HH24:MI') AS WO_START_DATE,
        WTC.TASK_CARD,
        WTC.DEFECT_TYPE || WTC.DEFECT AS DEFECT,
        TRIM(NVL(PM.PN, NVL(WTCP.PN, WTC.PN))) AS MASTERPN,
        MAX(PM.CATEGORY) AS CATEGORY,
        WTC.TASK_CARD_CATEGORY,
        MAX(WTC.PLANNING_PRIORITY) AS PLANNING_PRIORITY,
        MAX(WTC.TASK_CARD_DESCRIPTION) AS TASK_CARD_DESCRIPTION,
        DATE1 AS START_DATE,
        DATE2 AS END_DATE
    FROM ODB.WO WO
        LEFT JOIN ODB.WO_TASK_CARD       WTC  ON WTC.WO = WO.WO
        LEFT JOIN ODB.WO_TASK_CARD_PN    WTCP ON WTCP.TASK_CARD = WTC.TASK_CARD AND WTCP.WO = WTC.WO AND WTCP.TASK_CARD_PN = WTC.PN AND WTCP.TASK_CARD_PN_SN = WTC.PN_SN
        LEFT JOIN ODB.PN_INTERCHANGEABLE PI   ON PI.PN_INTERCHANGEABLE = NVL(WTCP.PN, WTC.PN)
        LEFT JOIN ODB.PN_MASTER          PM   ON PI.PN = PM.PN
        LEFT JOIN STATION_INFO           LM   ON WO.LOCATION = LM.LOCATION,
        PARAMS
    WHERE WO.STATUS = 'OPEN'
      AND WTC.STATUS IN ('OPEN','INPROGRESS')
      AND WTC.TASK_CARD NOT LIKE 'NR-%'
      AND NVL(LM.STATION, WO.LOCATION) IN ('YUL','YYZ','YVR','YYC','YEG','YWG','YOW','YHZ','YYT','YQB',
                                           'LAX','SFO','SEA','LAS','MCO','MIA','GRU','SJO','LHR','FRA','FLL','OPB')
      AND WO.SITE NOT IN ('SPP')
      AND TRIM(NVL(PM.PN, NVL(WTCP.PN, WTC.PN))) IS NOT NULL
      AND TRUNC(WO.SCHEDULE_START_DATE) + WO.SCHEDULE_START_HOUR/24 + WO.SCHEDULE_START_MINUTE/24/60 >= DATE1
      AND TRUNC(WO.SCHEDULE_START_DATE) + WO.SCHEDULE_START_HOUR/24 + WO.SCHEDULE_START_MINUTE/24/60 <= DATE2
    GROUP BY
        WO.WO,
        WO.AC,
        NVL(LM.STATION, WO.LOCATION),
        WO.SCHEDULE_START_DATE,
        WO.SCHEDULE_START_HOUR,
        WO.SCHEDULE_START_MINUTE,
        WTC.TASK_CARD,
        WTC.DEFECT_TYPE || WTC.DEFECT,
        NVL(PM.PN, NVL(WTCP.PN, WTC.PN)),
        WTC.TASK_CARD_CATEGORY,
        DATE1,
        DATE2

    UNION ALL

    -- ************** NON-ROUTINE TASK_CARD
    SELECT
        WO.WO,
        WO.AC,
        NVL(LM.STATION, WO.LOCATION) AS WO_STATION,
        TO_CHAR(TRUNC(WO.SCHEDULE_START_DATE) + WO.SCHEDULE_START_HOUR/24 + WO.SCHEDULE_START_MINUTE/24/60,'YYYY-MM-DD HH24:MI') AS WO_START_DATE,
        WTC.TASK_CARD,
        WTC.DEFECT_TYPE || WTC.DEFECT AS DEFECT,
        TRIM(NVL(PM.PN, NVL(DRPN.PN, NVL(WTCP.PN, WTC.PN)))) AS MASTERPN,
        MAX(PM.CATEGORY) AS CATEGORY,
        MAX(WTC.TASK_CARD_CATEGORY) AS TASK_CARD_CATEGORY,
        MAX(WTC.PLANNING_PRIORITY) AS PLANNING_PRIORITY,
        MAX(WTC.TASK_CARD_DESCRIPTION) AS TASK_CARD_DESCRIPTION,
        DATE1 AS START_DATE,
        DATE2 AS END_DATE
    FROM ODB.WO WO
        LEFT JOIN ODB.WO_TASK_CARD       WTC  ON WTC.WO = WO.WO
        LEFT JOIN ODB.WO_TASK_CARD_PN    WTCP ON WTCP.TASK_CARD = WTC.TASK_CARD AND WTCP.WO = WTC.WO AND WTCP.TASK_CARD_PN = WTC.PN AND WTCP.TASK_CARD_PN_SN = WTC.PN_SN
        LEFT JOIN ODB.DEFECT_REPORT_PN   DRPN ON DRPN.DEFECT_TYPE = WTC.DEFECT_TYPE AND DRPN.DEFECT = WTC.DEFECT AND DRPN.DEFECT_ITEM = WTC.DEFECT_ITEM
        LEFT JOIN ODB.PN_INTERCHANGEABLE PI   ON PI.PN_INTERCHANGEABLE = NVL(DRPN.PN, WTCP.PN)
        LEFT JOIN ODB.PN_MASTER          PM   ON PI.PN = PM.PN
        LEFT JOIN STATION_INFO           LM   ON WO.LOCATION = LM.LOCATION,
        ODB.PARAMS
    WHERE WO.STATUS = 'OPEN'
      AND WTC.STATUS IN ('OPEN','INPROGRESS')
      AND WTC.TASK_CARD LIKE 'NR-%'
      AND NVL(LM.STATION, WO.LOCATION) IN ('YUL','YYZ','YVR','YYC','YEG','YWG','YOW','YHZ','YYT','YQB',
                                           'LAX','SFO','SEA','LAS','MCO','MIA','GRU','SJO','LHR','FRA','FLL','OPB')
      AND WO.SITE NOT IN ('SPP')
      AND TRIM(NVL(PM.PN, NVL(DRPN.PN, NVL(WTCP.PN, WTC.PN)))) IS NOT NULL
      AND TRUNC(WO.SCHEDULE_START_DATE) + WO.SCHEDULE_START_HOUR/24 + WO.SCHEDULE_START_MINUTE/24/60 >= DATE1
      AND TRUNC(WO.SCHEDULE_START_DATE) + WO.SCHEDULE_START_HOUR/24 + WO.SCHEDULE_START_MINUTE/24/60 <= DATE2
    GROUP BY
        WO.WO,
        WO.AC,
        NVL(LM.STATION, WO.LOCATION),
        TO_CHAR(TRUNC(WO.SCHEDULE_START_DATE) + WO.SCHEDULE_START_HOUR/24 + WO.SCHEDULE_START_MINUTE/24/60,'YYYY-MM-DD HH24:MI'),
        WTC.TASK_CARD,
        WTC.DEFECT_TYPE || WTC.DEFECT,
        TRIM(NVL(PM.PN, NVL(DRPN.PN, NVL(WTCP.PN, WTC.PN)))),
        DATE1,
        DATE2
)
,SMC_INFO AS
(
	SELECT
		TRIM(TCPN.WO)
			|| '-' || TRIM(TCPN.WO_STATION)
			|| '-' || TRIM(TCPN.TASK_CARD)
			|| '-' || TRIM(TCPN.MASTERPN)
			AS "SMC_ID"
		,TCPN.WO
		,TCPN.AC
		,TCPN.WO_STATION
		,TCPN.WO_START_DATE
		,TCPN.TASK_CARD
		,TCPN.DEFECT
		,TCPN.MASTERPN
		,TCPN.CATEGORY
    	,TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') AS "CREATED_DATE"
    	,TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') AS "MODIFIED_DATE"
    	,'SYSTEM' AS "MODIFIED_BY"
		,1 AS "STATUS_ID"
		,TCPN.START_DATE
		,TCPN.END_DATE
	FROM TASK_CARD_PN_INFO TCPN
	ORDER BY
		WO
		,TASK_CARD
		,MASTERPN
)

SELECT * FROM SMC_INFO
  `;
  const smcWoData = await dbConnector.executeQuery(smcWoDataSql);

  LOGGER.info(
    `Query completed in ${Date.now() - startTime}ms. Retrieved ${
      smcWoData.length
    } engine data records`
  );
  return smcWoData as SmcWoData[];
};

const dataExtractionService: DataExtractionService = {
  getVendorData,
  getWOData,
  getPOData: getPoData,
  getTaskCardData,
  getEngineData: getEnginesData,
  getDeletedWoData,
  getSmcWoData,
};

export {
  getVendorData,
  getWOData,
  getPoData,
  getTaskCardData,
  getEnginesData,
  getDeletedWoData,
  getSmcWoData,
};

export default dataExtractionService;
