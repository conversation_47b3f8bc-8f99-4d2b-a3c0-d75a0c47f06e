// Type declarations for external modules

declare module 'dbaas-logger' {
  export interface DbaasLoggerConfig {
    streamName?: string;
    debugMode?: string;
  }

  export const dbaasLoggerConfig: DbaasLoggerConfig;

  export const dbaasLogLevels: {
    error: string;
  };

  export function start(context: any, serviceType: string, serviceName: string, message: string): void;
  export function logMessage(context: any, serviceType: string, serviceName: string, message: string, data: any, level?: string): any;
  export function logServiceRequest(context: any, serviceType: string, serviceName: string, message: string, data: string, type: string): string;
  export function logServiceResponse(context: any, serviceType: string, serviceName: string, message: string, data: string, type: string, requestId: string): void;
  export function logServiceError(context: any, serviceType: string, serviceName: string, message: string, errorCode: string, error: any, requestId: string): void;
  export function logCompletion(context: any, serviceType: string, serviceName: string, message: string, status: string, response: any): void;
  export function sendLogsToKinesis(context: any): any;
}

declare module 'ac-utils' {
  export class secretsManagerCache {
    getSecret(secretName: string): Promise<string>;
  }

  export function retrier(options: {
    callback: () => Promise<any>;
    attempts: number;
    debug: boolean;
  }): Promise<any>;
}

declare module 'fast-csv' {
  export function write(data: any[], options?: { headers?: boolean }): {
    on(event: 'data', callback: (row: string) => void): any;
    on(event: 'end', callback: () => void): any;
    on(event: 'error', callback: (error: Error) => void): any;
  };
}
