// Common types for the delta-load-extraction stack

export interface DatabaseProxyCredentials {
  API_KEY: string;
  API_HOST: string;
}

export interface VendorData {
  RELATION_CODE: string;
  NAME: string;
  MAIL_EMAIL: string;
  MAIL_PHONE: string;
  MAIL_ADDRESS_1: string;
  MAIL_ADDRESS_2: string;
  MAIL_CITY: string;
  MAIL_STATE: string;
  MAIL_COUNTRY: string;
  MAIL_POST: string;
}

export interface WorkOrderData {
  WO: string;
  AC: string;
  AC_TYPE: string;
  EVENT_ID: string;
  LOCATION: string;
  WO_DESCRIPTION: string;
  VENDOR: string;
  SITE: string;
  PROJECT: string;
  WO_CATEGORY: string;
  PRIORITY: string;
  EXTERNAL_REFERENCE: string;
  STATUS: string;
  DURATION: number;
  PERCENT_COMPLETED: number;
  TOTAL_TC: number;
  TOTAL_CANCEL_TC: number;
  TOTAL_ROUTINE_TC: number;
  OPEN_ROUTINE_TC: number;
  CLOSED_ROUTINE_TC: number;
  CANCEL_ROUTINE_TC: number;
  TOTAL_NON_ROUTINE_TC: number;
  OPEN_NON_ROUTINE_TC: number;
  CLOSED_NON_ROUTINE_TC: number;
  CANCEL_NON_ROUTINE_TC: number;
  TOTAL_MH: number;
  TOTAL_MH_COMPLETED: number;
  MH_ROUTINE_CHECK_COMPLETION: number;
  TYPE: string;
  AC_SN: string;
  AC_FLIGHT_HOURS: number;
  AC_FLIGHT_MINUTES: number;
  AC_CYCLES: number;
  LAST_AC_REGISTRATION: string;
  OPERATOR: string;
  FLIGHT: string;
  ORIGIN: string;
  DESTINATION: string;
  FLIGHT_DATE: Date;
  ON_HOUR: number;
  ON_MINUTE: number;
  SCHEDULE_START_DATE: Date;
  SCHEDULE_START_HOUR: number;
  SCHEDULE_START_MINUTE: number;
  SCHEDULE_COMPLETION_DATE: Date;
  SCHEDULE_COMPLETION_HOUR: number;
  SCHEDULE_COMPLETION_MINUTE: number;
  SCHEDULE_ORG_COMPLETION_DATE: Date;
  SCHEDULE_ORG_COMPLETION_HOUR: number;
  SCHEDULE_ORG_COMPLETION_MINUTE: number;
  ACTUAL_START_DATE: Date;
  ACTUAL_START_HOUR: number;
  ACTUAL_START_MINUTE: number;
  CREATED_DATE: Date;
  CREATED_BY: string;
  MODIFIED_DATE: Date;
  MODIFIED_BY: string;
  TIME_ZONE_NAME: string;
}

export interface PurchaseOrderData {
  WO: string;
  EXTERNAL_REFERENCE: string;
  CURRENCY_CODE: string;
  SUPPLIER_CODE: string;
  SUPPLIER_ADDRESS1: string;
  SUPPLIER_ADDRESS2: string;
  SUPPLIER_CITY: string;
  SUPPLIER_POST: string;
  SUPPLIER_STATE: string;
  SUPPLIER_COUNTRY: string;
  SUPPLIER_PHONE: string;
  SUPPLIER_FAX: string;
  SUPPLIER_CELL: string;
  SUPPLIER_EMAIL: string;
  SUPPLIER: string;
}

export interface TaskCardData {
  WO: string;
  EVENT_ID: string;
  PERIOD: string;
  TASK_CARD: string;
  PN: string;
  PN_SN: string;
  PID: string;
  TID: string;
  UNIQUE_KEY: string;
}

export interface EngineData {
  AC: string;
  MSN: string;
  AC_TYPE: string;
  AC_SERIES: string;
  ENGINE: string;
  PN_DESCRIPTION: string;
  PN: string;
  MASTER_PN: string;
  SN: string;
  TOP_PN: string;
  INSTALLED_AC: string;
  INSTALLED_POSITION: string;
  ALIAS: string;
  INSTALLED_DATE: Date;
  PRORATED_FLAG: string;
  GRB: string;
  HSN: number;
  CSN: number;
}

export interface SmcWoData {
  SMC_ID: string;
  WO: string;
  AC: string;
  WO_STATION: string;
  WO_START_DATE: Date;
  TASK_CARD: string;
  DEFECT: string;
  MASTERPN: string;
  CATEGORY: string;
  CREATED_DATE: Date;
  MODIFIED_DATE: Date;
  MODIFIED_BY: string;
  STATUS_ID: string;
  START_DATE: Date;
  END_DATE: Date;
}

export interface DeletedWoData {
  WO: string;
  EVENT_ID: string;
}

export interface S3UploadParams {
  Bucket: string;
  Key: string;
  Body: string | Buffer;
  ContentType: string;
}

export interface LambdaResponse {
  statusCode: number;
  body: string;
}

export interface ErrorNotification {
  subject: string;
  message: string;
  topicArn: string;
}

// Environment variables interface
export interface EnvironmentVariables {
  TRAX_DB_SECRET: string;
  DB_CONNECTION_RETRY_ATTEMPTS: string;
  DB_CONNECTION_MIN_POOL_SIZE: string;
  DB_CONNECTION_MAX_POOL_SIZE: string;
  DISTRIBUTION_BUCKET_NAME: string;
  KNEX_DEBUG: string;
  SNS_TOPIC_ARN: string;
  PENDING_FILE_GENERATING_PATH: string;
  VENDOR_GENERATING_FILE_PATH: string;
  FILE_PREFIX_VENDOR: string;
  WO_GENERATING_FILE_PATH: string;
  FILE_PREFIX_WO: string;
  PO_GENERATING_FILE_PATH: string;
  FILE_PREFIX_PO: string;
  TASK_CARD_GENERATING_FILE_PATH: string;
  FILE_PREFIX_TASK_CARD: string;
  ENGINE_DATA_GENERATING_FILE_PATH: string;
  FILE_PREFIX_ENGINE_DATA: string;
}

// Knex query builder types - using the actual Knex type
import * as knex from "knex";
export type KnexQueryBuilder = knex.Knex;

// Service interfaces
export interface DataExtractionService {
  getVendorData(): Promise<VendorData[]>;
  getWOData(startDate: Date, endDate: Date): Promise<WorkOrderData[]>;
  getPOData(startDate: Date, endDate: Date): Promise<PurchaseOrderData[]>;
  getTaskCardData(): Promise<TaskCardData[]>;
  getEngineData(): Promise<EngineData[]>;
  getDeletedWoData(): Promise<DeletedWoData[]>;
  getSmcWoData(): Promise<SmcWoData[]>;
}

export interface FileHandleService {
  generateCsv(data: any[]): Promise<Buffer>;
  vendorFileUpload(csvContent: string | Buffer): Promise<S3UploadParams>;
  woFileUpload(csvContent: string | Buffer): Promise<S3UploadParams>;
  poFileUpload(csvContent: string | Buffer): Promise<S3UploadParams>;
  taskCardFileUpload(csvContent: string | Buffer): Promise<S3UploadParams>;
  engineDataFileUpload(csvContent: string | Buffer): Promise<S3UploadParams>;
  notifyFailedEvents(subject: string, message: string): Promise<void>;
  deletedWoDataFileUpload(csvContent: string | Buffer): Promise<S3UploadParams>;
  smcWoDataFileUpload(csvContent: string | Buffer): Promise<S3UploadParams>;
}

// Adaptor interfaces
export interface OracleAdaptor {
  connectOracleDatabase(): Promise<KnexQueryBuilder>;
  closeConnection(): Promise<void>;
  isConnectionHealthy(): Promise<boolean>;
}

export interface S3Adaptor {
  putObject(params: S3UploadParams): Promise<any>;
}

export interface SNSAdaptor {
  publishMessage(params: {
    TopicArn: string;
    Subject: string;
    Message: string;
  }): Promise<any>;
}

// Utility interfaces
export interface DateUtil {
  getDateTimeString(): string;
  formatDate(date: Date): string;
}

export interface FlowUtil {
  initiateGenericFlow(event: any, context: any, serviceName: string): void;
}

export interface CSVGenerator {
  generateCSV(data: any[]): Promise<Buffer>;
}

// Logger interface
export interface Logger {
  start(event: any, message: string): void;
  info(message: string, ...args: any[]): void;
  error(message: string, error?: any): void;
  sendLogsToKinesis(): void;
  constants: {
    SERVICE_NAME_VENDOR: string;
    SERVICE_NAME_WO: string;
    SERVICE_NAME_PO: string;
    SERVICE_NAME_TASK_CARD: string;
    SERVICE_NAME_ENGINE_DATA: string;
  };
}
