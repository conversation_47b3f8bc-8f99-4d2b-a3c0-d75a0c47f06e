import {
  APIGatewayProxyEvent,
  APIGatewayProxyResult,
  Context,
} from "aws-lambda";
import { dataExtractionService, fileHandleService } from "../services";
import { LOGGER, constant } from "../utils";
import { DeletedWoData, WorkOrderData } from "../types";

export const handler = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  try {
    LOGGER.initialize(
      event,
      context,
      constant.SERVICE_TYPE,
      constant.SERVICE_NAME_WO
    );

    let endDate = new Date();
    let startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 1);

    const workorderData: WorkOrderData[] =
      await dataExtractionService.getWOData(startDate, endDate);
    LOGGER.info(`Workorder data retrieved. Count: ${workorderData.length}`);

    const csvStream: Buffer = await fileHandleService.generateCsv(
      workorderData
    );
    LOGGER.info("CSV stream generated");

    await fileHandleService.woFileUpload(csvStream);

    LOGGER.info("WO CSV uploaded to S3");

    const deletedWoData: DeletedWoData[] =
      await dataExtractionService.getDeletedWoData();
    LOGGER.info(`Deleted WO data retrieved. Count: ${deletedWoData.length}`);

    const deletedWoCsvStream: Buffer = await fileHandleService.generateCsv(
      deletedWoData
    );
    LOGGER.info("Deleted WO CSV stream generated");

    await fileHandleService.deletedWoDataFileUpload(deletedWoCsvStream);

    LOGGER.info("Deleted WO CSV uploaded to S3");

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Workorder data extracted successfully",
        count: workorderData.length,
      }),
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    LOGGER.error("Error extracting workorder data:", error);

    await fileHandleService.notifyFailedEvents(
      "Error extracting workorder data",
      errorMessage
    );

    throw new Error(`Error extracting workorder data: ${errorMessage}`);
  } finally {
    LOGGER.sendLogsToKinesis();
  }
};
