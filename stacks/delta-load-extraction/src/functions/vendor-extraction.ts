import {
  APIGatewayProxyEvent,
  APIGatewayProxyResult,
  Context,
} from "aws-lambda";
import { dataExtractionService, fileHandleService } from "../services";
import { LOGGER, constant } from "../utils";
import { VendorData } from "../types";

export const handler = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  try {
    LOGGER.initialize(
      event,
      context,
      constant.SERVICE_TYPE,
      constant.SERVICE_NAME_VENDOR
    );

    const vendorData: VendorData[] =
      await dataExtractionService.getVendorData();
    LOGGER.info(`Vendor data retrieved. Count: ${vendorData.length}`);

    const csvStream: Buffer = await fileHandleService.generateCsv(vendorData);
    LOGGER.info("CSV stream generated");

    await fileHandleService.vendorFileUpload(csvStream);

    LOGGER.info("CSV uploaded to S3");

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Vendor data extracted successfully",
        count: vendorData.length,
      }),
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    LOGGER.error("Error extracting vendor data:", error);

    await fileHandleService.notifyFailedEvents(
      "Error extracting vendor data",
      errorMessage
    );

    throw new Error(`Error extracting vendor data: ${errorMessage}`);
  } finally {
    LOGGER.sendLogsToKinesis();
  }
};
