import {
  APIGatewayProxyEvent,
  APIGatewayProxyResult,
  Context,
} from "aws-lambda";
import { dataExtractionService, fileHandleService } from "../services";
import { LOGGER, constant } from "../utils";
import { PurchaseOrderData } from "../types";

export const handler = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  try {
    LOGGER.initialize(
      event,
      context,
      constant.SERVICE_TYPE,
      constant.SERVICE_NAME_PO
    );

    // Set default date range for PO data
    let endDate = new Date();
    let startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 30); // Last 30 days

    const poData: PurchaseOrderData[] = await dataExtractionService.getPOData(
      startDate,
      endDate
    );

    LOGGER.info(`PO data retrieved. Count: ${poData.length}`);

    const csvStream: Buffer = await fileHandleService.generateCsv(poData);
    LOGGER.info("CSV stream generated");

    await fileHandleService.poFileUpload(csvStream);

    LOGGER.info("CSV uploaded to S3");

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "PO data extracted successfully",
        count: poData.length,
      }),
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    LOGGER.error("Error extracting PO data:", error);
    await fileHandleService.notifyFailedEvents(
      "Error extracting PO data",
      errorMessage
    );
    throw new Error(`Error extracting PO data: ${errorMessage}`);
  } finally {
    LOGGER.sendLogsToKinesis();
  }
};
