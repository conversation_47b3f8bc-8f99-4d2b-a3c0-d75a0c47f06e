import {
  APIGatewayProxyEvent,
  APIGatewayProxyResult,
  Context,
} from "aws-lambda";
import { dataExtractionService, fileHandleService } from "../services";
import { LOGGER, constant } from "../utils";
import { SmcWoData } from "../types";

export const handler = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  try {
    LOGGER.initialize(
      event,
      context,
      constant.SERVICE_TYPE,
      constant.SERVICE_NAME_SMC_WO_DATA
    );

    const smcWoData: SmcWoData[] =
      await dataExtractionService.getSmcWoData();

    LOGGER.info(`SMC WO data retrieved. Count: ${smcWoData.length}`);

    const csvStream: Buffer = await fileHandleService.generateCsv(smcWoData);
    LOGGER.info("CSV stream generated");

    await fileHandleService.smcWoDataFileUpload(csvStream);

    LOGGER.info("CSV uploaded to S3");

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "SMC WO data extracted successfully",
        count: smcWoData.length,
      }),
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    LOGGER.error("Error extracting SMC WO data:", error);
    await fileHandleService.notifyFailedEvents(
      "Error extracting SMC WO data",
      errorMessage
    );
    throw new Error(`Error extracting SMC WO data: ${errorMessage}`);
  } finally {
    LOGGER.sendLogsToKinesis();
  }
};