import {
  APIGatewayProxyEvent,
  APIGatewayProxyResult,
  Context,
} from "aws-lambda";
import { dataExtractionService, fileHandleService } from "../services";
import { LOGGER, constant } from "../utils";
import { TaskCardData } from "../types";

export const handler = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  try {
    LOGGER.initialize(
      event,
      context,
      constant.SERVICE_TYPE,
      constant.SERVICE_NAME_TASK_CARD
    );

    const taskCardData: TaskCardData[] =
      await dataExtractionService.getTaskCardData();

    LOGGER.info(`Task Card data retrieved. Count: ${taskCardData.length}`);

    const csvStream: Buffer = await fileHandleService.generateCsv(taskCardData);
    LOGGER.info("CSV stream generated");

    await fileHandleService.taskCardFileUpload(csvStream);

    LOGGER.info("CSV uploaded to S3");

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Task Card data extracted successfully",
        count: taskCardData.length,
      }),
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    LOGGER.error("Error extracting Task Card data:", error);
    await fileHandleService.notifyFailedEvents(
      "Error extracting Task Card data",
      errorMessage
    );
    throw new Error(`Error extracting Task Card data: ${errorMessage}`);
  } finally {
    LOGGER.sendLogsToKinesis();
  }
};
