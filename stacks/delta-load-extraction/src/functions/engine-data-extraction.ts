import {
  APIGatewayProxyEvent,
  APIGatewayProxyResult,
  Context,
} from "aws-lambda";
import { dataExtractionService, fileHandleService } from "../services";
import { LOGGER, constant } from "../utils";
import { EngineData } from "../types";

export const handler = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  try {
    LOGGER.initialize(
      event,
      context,
      constant.SERVICE_TYPE,
      constant.SERVICE_NAME_ENGINE_DATA
    );

    const enginesData: EngineData[] =
      await dataExtractionService.getEngineData();

    LOGGER.info(`Engines data retrieved. Count: ${enginesData.length}`);

    const csvStream: Buffer = await fileHandleService.generateCsv(enginesData);
    LOGGER.info("CSV stream generated");

    await fileHandleService.engineDataFileUpload(csvStream);

    LOGGER.info("CSV uploaded to S3");

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Engine data extracted successfully",
        count: enginesData.length,
      }),
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    LOGGER.error("Error extracting Engine data:", error);
    await fileHandleService.notifyFailedEvents(
      "Error extracting Engine data",
      errorMessage
    );
    throw new Error(`Error extracting Engine data: ${errorMessage}`);
  } finally {
    LOGGER.sendLogsToKinesis();
  }
};
