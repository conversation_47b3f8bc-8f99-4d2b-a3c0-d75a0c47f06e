import { Stack, StackProps, Fn } from "aws-cdk-lib";
import { Vpc, SecurityGroup } from "aws-cdk-lib/aws-ec2";
import { Role } from "aws-cdk-lib/aws-iam";
import { Construct } from "constructs";
import {
  AppConfig,
  DefaultStackSynthesizer,
  DeltaLoadExtractionStackConfig,
} from "../../utils/config-util";

import { DataExtractionLambdasStack } from "./resources/data-extraction-lambdas";
import { DataExtractionStateMachineStack } from "./resources/data-extraction-state-machine";
export interface DeltaLoadExtractionStackProps extends StackProps {
  config: AppConfig;
  synthesizer: DefaultStackSynthesizer;
  stackConfigProps: DeltaLoadExtractionStackConfig;
  errorNotifyTopicArn: string;
}

export class DeltaLoadExtractionStack extends Stack {
  constructor(
    scope: Construct,
    id: string,
    props: DeltaLoadExtractionStackProps
  ) {
    super(scope, id, {
      env: {
        account: props.config.account,
        region: props.config.region,
      },
      stackName: `${props.stackConfigProps.stackName}-${props.config.environment}`,
      ...props,
      synthesizer: props.synthesizer,
    });

    const vpc = Vpc.fromLookup(this, "VPC", {
      vpcId: props.config.vpc.vpcId,
      isDefault: false,
    });

    const securityGroup = SecurityGroup.fromSecurityGroupId(
      this,
      "sgIdDigitalOdsRdsCredentials",
      props.config.vpc.securityGroupId
    );

    const roleArn = Fn.importValue(
      props.config.iamRole.ingestionLambdaRoleExportName
    );

    const dataExtractionRole = Role.fromRoleArn(
      this,
      "ImportedIngestionLambdaRole",
      roleArn
    );

    const lambdasStack = new DataExtractionLambdasStack(
      this,
      "DataExtractionLambdas",
      {
        vpc,
        securityGroup,
        config: props.config,
        stackConfigProps: props.stackConfigProps,
        errorNotifyTopicArn: props.errorNotifyTopicArn,
        lambdaRole: dataExtractionRole,
      }
    );

    new DataExtractionStateMachineStack(this, "DataExtractionStateMachine", {
      config: props.config,
      stackConfigProps: props.stackConfigProps,
      errorNotifyTopicArn: props.errorNotifyTopicArn,
      vendorExtractionLambda: lambdasStack.vendorExtractionLambda,
      woExtractionLambda: lambdasStack.woExtractionLambda,
      poExtractionLambda: lambdasStack.poExtractionLambda,
      taskCardExtractionLambda: lambdasStack.taskCardExtractionLambda,
      engineDataExtractionLambda: lambdasStack.engineDataExtractionLambda,
      smcWoDataLambdaConstruct: lambdasStack.smcWoDataLambdaConstruct,
    });
  }
}
